'use server';

import { get } from '../../../lib/api';
// Assuming the appointment structure is similar to the one defined elsewhere
// If not, adjust this import or definition accordingly.
import { AppointmentResponse } from './createAppointment';

// Define the structure for the range object
interface DateRange {
  start: string; // ISO Date string
  end: string; // ISO Date string
}

// Define the expected response type for calendar appointments
export interface CalendarAppointmentsResponse {
  appointments: AppointmentResponse[]; // Array of appointment objects
  blockedDays: string[]; // Array of date strings, e.g., "YYYY-MM-DD"
  range: DateRange; // Object describing the fetched date range
}

/**
 * Fetches appointments and blocked days for a specific month and year
 * to populate the physician's calendar.
 * @param month The month number (1-12).
 * @param year The year.
 * @returns A promise resolving to the calendar appointments response.
 */
export async function getCalendarAppointments(
  month: number,
  year: number
): Promise<CalendarAppointmentsResponse> {
  // Validate month and year
  if (month < 1 || month > 12) {
    throw new Error('Invalid month number. Must be between 1 and 12.');
  }
  if (year < 1900 || year > 2100) {
    // Basic year range validation
    throw new Error('Invalid year.');
  }

  try {
    // Construct the endpoint URL with query parameters
    const endpoint = `/appointments/physician-calendar?month=${month}&year=${year}`;

    // Call the get function from lib/api
    const response = await get<CalendarAppointmentsResponse>(endpoint);
    console.log(
      'API Response for Calendar Appointments:',
      JSON.stringify(response, null, 2)
    ); // Log the raw response

    // Basic validation of the response structure
    if (
      !response ||
      !Array.isArray(response.appointments) ||
      !Array.isArray(response.blockedDays) ||
      !response.range ||
      !response.range.start ||
      !response.range.end
    ) {
      console.warn(
        `Received unexpected response format for calendar appointments:`,
        response
      );
      // Return a default empty state or throw a more specific error
      return {
        appointments: [],
        blockedDays: [],
        range: { start: '', end: '' }
      };
    }

    return response;
  } catch (error) {
    console.error('Error fetching calendar appointments:', error);

    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(
        `Failed to fetch calendar appointments: ${error.message as string}`
      );
    }

    throw new Error(
      'Failed to fetch calendar appointments due to an unexpected error.'
    );
  }
}
