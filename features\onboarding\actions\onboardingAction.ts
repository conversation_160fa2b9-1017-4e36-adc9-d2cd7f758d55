'use server';

import { api } from '../../../lib/axios';

// Define the onboarding data type based on the backend DTO
export interface OnboardingData {
  user_id: string;
  name: string;
  type: 'individual' | 'organization'; // tenant_type enum
  number_of_employees?: number;
  number_of_patients?: number;
  reason_register: string;
  speciality: number[];
}

// Define the response type
export interface OnboardingResponse {
  message: string;
}

/**
 * Submits user onboarding data to the backend
 * @param data Onboarding data
 * @returns Response message
 */
export async function submitOnboarding(
  data: OnboardingData
): Promise<OnboardingResponse> {
  try {
    // Make the API request
    const response = await api.post<OnboardingResponse>(
      '/user/onboarding',
      data
    );

    return response.data;
  } catch (error) {
    console.error('Error submitting onboarding data:', error);

    // Handle error
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(error.message as string);
    }

    throw new Error('Failed to submit onboarding data');
  }
}
