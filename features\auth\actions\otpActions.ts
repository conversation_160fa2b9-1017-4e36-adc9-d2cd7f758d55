'use server';

import { post } from '../../../lib/api';

/**
 * Sends an OTP verification code to the user's phone
 * @param userId User ID
 * @param phonePrefix Phone prefix (country code)
 * @param phone Phone number
 * @returns Response message
 */
export async function sendOTP(
  userId: string,
  phonePrefix: string,
  phone: string
): Promise<{ message: string }> {
  try {
    return await post<{ message: string }>('/auth/send-otp', {
      user_id: userId,
      phone_prefix: phonePrefix,
      phone: phone
    });
  } catch (error) {
    console.error('Error sending OTP:', error);

    // Handle error
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(error.message as string);
    }

    throw new Error('Failed to send verification code');
  }
}

/**
 * Verifies an OTP code
 * @param userId User ID
 * @param code OTP code
 * @returns Response message
 */
export async function verifyOTP(
  userId: string,
  code: string
): Promise<{ message: string }> {
  try {
    return await post<{ message: string }>('/auth/verify-otp/', {
      user_id: userId,
      code: code
    });
  } catch (error) {
    console.error('Error verifying OTP:', error);

    // Handle error
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(error.message as string);
    }

    throw new Error('Failed to verify code');
  }
}
