import React from 'react';

const OrganizationIcon = () => {
  return (
    <div>
      <svg
        width="49.000000"
        height="49.000000"
        viewBox="0 0 49 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <clipPath id="clip4495_5315">
            <rect
              id="svg"
              width="49.000000"
              height="49.000000"
              fill="white"
              fillOpacity="0"
            />
          </clipPath>
        </defs>
        <g clipPath="url(#clip4495_5315)">
          <path
            id="path"
            d="M0 0L49 0L49 49L0 49L0 0Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M49 0L49 49L0 49L0 0L49 0Z"
            stroke="#000000"
            strokeOpacity="0"
            strokeWidth="2.000000"
            strokeLinejoin="round"
          />
          <path
            id="path"
            d="M6.14 44.35L6.12 44.37C5.28 44.37 4.62 43.71 4.62 42.87C4.62 42.03 5.28 41.37 6.12 41.37L6.14 41.39L6.14 44.35ZM42.85 41.39L42.87 41.37C43.71 41.37 44.37 42.03 44.37 42.87C44.37 43.71 43.71 44.37 42.87 44.37L42.85 44.35L42.85 41.39Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M6.12 42.87L42.87 42.87"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="3.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M11.68 42.85L11.7 42.87C11.7 43.71 11.04 44.37 10.2 44.37C9.36 44.37 8.7 43.71 8.7 42.87L8.72 42.85L11.68 42.85ZM40.27 42.85L40.29 42.87C40.29 43.71 39.63 44.37 38.79 44.37C37.95 44.37 37.29 43.71 37.29 42.87L37.31 42.85L40.27 42.85Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M10.2 42.87L10.2 10.2C10.2 9.08 10.6 8.11 11.4 7.32C12.2 6.52 13.16 6.12 14.29 6.12L34.7 6.12C35.83 6.12 36.79 6.52 37.59 7.32C38.39 8.11 38.79 9.08 38.79 10.2L38.79 42.87"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="3.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M19.85 42.85L19.87 42.87C19.87 43.71 19.21 44.37 18.37 44.37C17.53 44.37 16.87 43.71 16.87 42.87L16.89 42.85L19.85 42.85ZM32.1 42.85L32.12 42.87C32.12 43.71 31.46 44.37 30.62 44.37C29.78 44.37 29.12 43.71 29.12 42.87L29.14 42.85L32.1 42.85Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M18.37 42.87L18.37 34.7C18.37 33.58 18.77 32.61 19.57 31.82C20.36 31.02 21.33 30.62 22.45 30.62L26.54 30.62C27.66 30.62 28.63 31.02 29.42 31.82C30.22 32.61 30.62 33.58 30.62 34.7L30.62 42.87"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="3.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M20.43 19.85L20.41 19.87C19.57 19.87 18.91 19.21 18.91 18.37C18.91 17.53 19.57 16.87 20.41 16.87L20.43 16.89L20.43 19.85ZM28.56 16.89L28.58 16.87C29.42 16.87 30.08 17.53 30.08 18.37C30.08 19.21 29.42 19.87 28.58 19.87L28.56 19.85L28.56 16.89Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M20.41 18.37L28.58 18.37"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="3.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M23.02 14.31L23 14.29C23 13.45 23.66 12.79 24.5 12.79C25.34 12.79 26 13.45 26 14.29L25.98 14.31L23.02 14.31ZM25.98 22.43L26 22.45C26 23.29 25.34 23.95 24.5 23.95C23.66 23.95 23 23.29 23 22.45L23.02 22.43L25.98 22.43Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M24.5 14.29L24.5 22.45"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="3.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
        </g>
      </svg>
    </div>
  );
};

export default OrganizationIcon;
