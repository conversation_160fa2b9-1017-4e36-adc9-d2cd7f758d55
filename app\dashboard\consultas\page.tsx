/* eslint-disable no-console */
'use client';

import React from 'react';
// import ConsultationForm from '@/features/consultation/components/ConsultationForm'
import { Search, Plus, ArrowUpDown, Ellipsis } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Avatar from '@/features/patients/assets/AvarDefault.png';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import FetchConsultations from '@/features/consultation/actions/fetchConsultation';

const consultations = FetchConsultations();

const Page = () => {
  const router = useRouter();

  const handleViewConsultation = (id: string) => {
    router.push(`/dashboard/consultas/${id}/consulta`);
  };

  return (
    <div className="">
      {/* TABLA */}
      <Table className="w-full border-collapse border border-gray-300 px-10 py-2">
        <TableHeader>
          <TableRow className="grid grid-cols-[20%,15%,15%,20%,20%,10%] items-center border-b border-gray-300 bg-gray-100 px-10">
            <TableHead className="text-left">Nombre</TableHead>
            <TableHead className="text-left">Fecha de consulta</TableHead>
            <TableHead className="text-left">Motivo</TableHead>
            <TableHead className="text-left">Tipo</TableHead>
            <TableHead className="text-left">Cargo</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {consultations.map((consultation, index) => (
            <TableRow
              key={index}
              className="grid grid-cols-[20%,15%,15%,20%,20%,10%] items-center border-b border-gray-200 px-10"
            >
              <TableCell className="flex items-center gap-[5px]">
                <Image
                  src={Avatar}
                  alt="Consultation Avatar"
                  className="h-10 w-10 rounded-[5px]"
                />
                {consultation.name}
              </TableCell>
              <TableCell>{consultation.consultationDate}</TableCell>
              <TableCell>{consultation.reason}</TableCell>
              <TableCell>{consultation.type}</TableCell>
              <TableCell>{consultation.mount}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="hover:text-blue-600">
                      <Ellipsis />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    side="bottom"
                    align="start"
                    className="w-48"
                  >
                    <DropdownMenuItem
                      onSelect={() => handleViewConsultation(consultation.id)}
                    >
                      Ver Consulta
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onSelect={() => console.log('Ver Perfil')}
                    >
                      Ver Perfil
                    </DropdownMenuItem>
                    <DropdownMenuItem onSelect={() => console.log('Editar')}>
                      Editar
                    </DropdownMenuItem>
                    <DropdownMenuItem onSelect={() => console.log('Eliminar')}>
                      Eliminar
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {/* FIN DE TABLA */}
    </div>
  );
};

export default Page;
