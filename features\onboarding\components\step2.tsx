'use client';
import { useEffect } from 'react';
import useOnboardingStore from '../context/onboardingStore';
import OrganizationIcon from '../../../components/icons/OrganizationIcon';
import ParticularIcon from '../../../components/icons/ParticularIcon';

const Step2 = ({
  onValidation
}: {
  onValidation: (isValid: boolean) => void;
}) => {
  const { selectedType, setStepData, switchUserType, stepData } =
    useOnboardingStore();
  const userName = stepData.name || '';

  useEffect(() => {
    onValidation(selectedType !== null);
  }, [selectedType, onValidation]);

  const handleSelection = (type: 'Particulares' | 'Organizacion') => {
    switchUserType(type);
    setStepData('selectedType', type);

    if (type === 'Organizacion') {
      setStepData('especialidad', undefined);
      setStepData('organizacionParticular', undefined);
    } else {
      setStepData('organizacion', undefined);
      setStepData('pacientes', undefined);
    }
  };

  return (
    <>
      <div className="mx-auto flex w-full max-w-lg flex-col items-center space-y-2 p-2">
        <h1 className="text-center text-2xl font-bold text-[#487FFA]">
          {userName
            ? `${userName}, ¿qué opción te describe mejor?`
            : '¿Qué opción te describe mejor?'}
        </h1>

        <p className="text-start text-base text-[#5F5F5F] sm:text-sm">
          No importa el tipo de clínica o consultorio que seas, Segimed te puede
          ayudar
        </p>

        <div className="flex w-full flex-col items-center space-y-4">
          <label
            className={`flex w-full cursor-pointer items-center gap-4 border border-[#487FFA] bg-[#FBFBFB] p-4 transition-all duration-200 ${
              selectedType === 'Particulares'
                ? 'border-[#487FFA] bg-[#E8EFFF] ring-2 ring-[#487FFA]'
                : 'border-gray-300'
            }`}
          >
            <input
              type="radio"
              name="userType"
              checked={selectedType === 'Particulares'}
              onChange={() => handleSelection('Particulares')}
              className="hidden h-5 w-5 accent-[#487FFA]"
            />
            <ParticularIcon />
            <div>
              <p className="font-semibold text-[#487FFA]">Particulares</p>
              <p className="text-sm text-[#808080]">
                Consultorio de médico particular, consultorio privado o
                prácticas pequeñas.
              </p>
            </div>
          </label>
        </div>

        <div className="flex w-full flex-col items-center space-y-4">
          <label
            className={`flex w-full cursor-pointer items-center gap-4 border border-[#487FFA] bg-[#FBFBFB] p-4 transition-all duration-200 ${
              selectedType === 'Organizacion'
                ? 'border-[#487FFA] bg-[#E8EFFF] ring-2 ring-[#487FFA]'
                : 'border-gray-300'
            }`}
          >
            <input
              type="radio"
              name="userType"
              checked={selectedType === 'Organizacion'}
              onChange={() => handleSelection('Organizacion')}
              className="hidden h-5 w-5 accent-[#487FFA]"
            />
            <OrganizationIcon />
            <div>
              <p className="font-semibold text-[#487FFA]">Organización</p>
              <p className="text-sm text-[#808080]">
                Centros médicos, redes de consultorios u hospitales.
              </p>
            </div>
          </label>
        </div>
      </div>
    </>
  );
};

export default Step2;
