'use client';

import React, { useState } from 'react';
import { Trash } from 'lucide-react';
import useConsultationStore from '../../context/consultationStore';

const TreatementPlan = () => {
  const [treatementInput, setTreatementInput] = useState('');
  const { treatementPlan, addTreatementPlan, removeTreatementPlan } =
    useConsultationStore();
  const [loading, setLoading] = useState(false);

  const saveToBackend = async (treatement: string) => {
    try {
      setLoading(true);
      const response = await fetch('/api/treatement', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ treatement })
      });

      if (!response.ok) {
        throw new Error('Error al guardar el procedimiento');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error al enviar los datos:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTreatementBlur = async () => {
    if (treatementInput.trim() !== '') {
      addTreatementPlan(treatementInput);
      await saveToBackend(treatementInput);
      setTreatementInput('');
    }
  };

  return (
    <div className="mx-auto w-full max-w-2xl rounded-lg border border-gray-300 bg-white  p-4">
      <h1 className="mb-2 text-sm font-semibold text-gray-800">
        Plan de tratamiento
      </h1>
      <div className="mb-4 flex gap-2">
        <input
          type="text"
          placeholder="Indicar Procedimiento..."
          className="w-full rounded border border-[#DCDBDB] bg-white p-2"
          value={treatementInput}
          onChange={(e) => setTreatementInput(e.target.value)}
          onBlur={handleTreatementBlur}
        />
        <button
          className="rounded bg-blue-500 px-2 py-2 text-white"
          onClick={handleTreatementBlur}
        >
          {loading ? 'Guardando...' : 'Agregar'}
        </button>
      </div>
      <div>
        {Object.entries(treatementPlan).map(([treatement]) => (
          <div
            key={treatement}
            className="mb-2 flex items-center justify-between rounded border border-[#DCDBDB] bg-gray-100 p-2"
          >
            <p className="font-lg">{treatement}</p>
            <button
              className="text-red-500"
              onClick={() => removeTreatementPlan(treatement)}
            >
              <Trash size={20} />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TreatementPlan;
