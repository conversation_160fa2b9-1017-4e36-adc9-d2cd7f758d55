export type Notification = {
  id: string;
  description: string;
  time: string;
  isRead: boolean;
  type: 'info' | 'warning' | 'success' | 'error' | 'patient' | 'system';
};

export async function getNotifications(): Promise<Notification[]> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return [
    {
      id: '1',
      description:
        'Santiago Pérez ha activado una alarma de emergencia por dificultad respiratoria.',
      time: 'Hace 2 minutos',
      isRead: false,
      type: 'patient'
    },
    {
      id: '2',
      description: 'Debes actualizar tu contraseña antes del 20 de marzo',
      time: 'Hace 1 hora',
      isRead: false,
      type: 'system'
    },
    {
      id: '3',
      description: 'Tu cita con el Dr. Smith está programada para mañana',
      time: 'Hace 1 hora',
      isRead: true,
      type: 'info'
    },
    {
      id: '1',
      description:
        'Santiago Pérez ha activado una alarma de emergencia por dificultad respiratoria.',
      time: 'Hace 2 minutos',
      isRead: false,
      type: 'patient'
    },
    {
      id: '2',
      description: 'Debes actualizar tu contraseña antes del 20 de marzo',
      time: 'Hace 1 hora',
      isRead: false,
      type: 'system'
    },
    {
      id: '3',
      description: 'Tu cita con el Dr. Smith está programada para mañana',
      time: 'Hace 1 hora',
      isRead: true,
      type: 'info'
    }
  ];
}

export async function markNotificationAsRead(id: string): Promise<void> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));
  // In a real app, this would make an API call
}

export async function markAllNotificationsAsRead(): Promise<void> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));
  // In a real app, this would make an API call
}
