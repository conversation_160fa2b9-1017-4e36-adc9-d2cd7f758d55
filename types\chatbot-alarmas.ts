export interface Message {
  id?: string;
  sender: 'user' | 'bot';
  message: string;
  timestamp?: Date;
}

export interface ChatSession {
  id: string;
  userId: string;
  messages: Message[];
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'completed' | 'cancelled';
  metadata?: {
    alarmType?: string;
    priority?: 'high' | 'medium' | 'low';
    resolution?: string;
  };
}

export interface AlarmasChatUIProps {
  alarmas: Alarm[];
}

export interface Alarm {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'resolved';
}
