'use server';

import { post } from '../../../lib/api';

// Define the appointment data type based on the backend DTO
export interface CreateAppointmentData {
  consultation_reason: string;
  start: string; // Expecting ISO date string e.g., "2025-04-29T15:00:00"
  end: string; // Expecting ISO date string e.g., "2025-04-29T15:45:00"
  patient_id: string;
  physician_id: string;
  comments?: string; // Optional comments
}

// Define the response type
export interface AppointmentResponse {
  id: string;
  consultation_reason: string;
  start: string;
  end: string;
  patient_id: string;
  physician_id: string;
  status: string;
  comments?: string;
  tenant_id: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Creates a new appointment
 * @param data Appointment data
 * @returns Created appointment data
 */
export async function createAppointment(
  data: CreateAppointmentData
): Promise<AppointmentResponse> {
  try {
    // Usar el nuevo cliente API que maneja la autenticación con cookies
    return await post<AppointmentResponse>('/appointments', data);
  } catch (error) {
    console.error('Error creating appointment:', error);

    // Handle error
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(error.message as string);
    }

    throw new Error('Failed to create appointment');
  }
}
