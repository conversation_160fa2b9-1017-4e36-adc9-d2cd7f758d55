/* Custom styles for the calendar */
.rbc-calendar {
  background-color: white;
  z-index: 1; /* Ensure calendar has a lower z-index than popover */
}

.rbc-toolbar {
  background-color: white;
  padding: 10px;
}

.rbc-month-view,
.rbc-time-view,
.rbc-agenda-view {
  background-color: white;
}

.rbc-day-bg {
  background-color: white;
}

.rbc-time-content {
  background-color: white;
}

.rbc-time-header {
  background-color: white;
}

.rbc-time-slot {
  background-color: white;
}

.rbc-event {
  background-color: #487ffa;
}

.rbc-today {
  background-color: #f0f7ff;
}

.rbc-off-range-bg {
  background-color: #f9f9f9;
}

.rbc-header {
  background-color: white;
  padding: 8px 0;
  font-weight: 500;
}

.rbc-time-header-content {
  background-color: white;
}

.rbc-row-bg {
  background-color: white;
}

.rbc-time-gutter {
  background-color: white;
}

.rbc-timeslot-group {
  border-color: #e5e7eb;
}

.rbc-time-view .rbc-header {
  border-color: #e5e7eb;
}

.rbc-day-slot .rbc-time-slot {
  border-color: #e5e7eb;
}

.rbc-time-content > * + * > * {
  border-color: #e5e7eb;
}

.rbc-month-row + .rbc-month-row {
  border-color: #e5e7eb;
}

.rbc-day-bg + .rbc-day-bg {
  border-color: #e5e7eb;
}

.rbc-time-header-content > .rbc-row > * + * {
  border-color: #e5e7eb;
}

.rbc-month-view {
  border-color: #e5e7eb;
}

.rbc-time-view {
  border-color: #e5e7eb;
}

.rbc-agenda-view {
  border-color: #e5e7eb;
}

.rbc-time-header.rbc-overflowing {
  border-color: #e5e7eb;
}

.rbc-time-content {
  border-color: #e5e7eb;
}

.rbc-time-header-content {
  border-color: #e5e7eb;
}

.rbc-time-header-gutter {
  background-color: white;
}

.rbc-label {
  padding: 5px;
}

/* Styles to fix z-index issues */
.rbc-overlay {
  z-index: 100; /* Lower than the date picker popover */
}

/* Ensure date picker has higher z-index than calendar */
.react-datepicker-popper {
  z-index: 9999 !important;
}

/* Disable past dates */
.rbc-day-bg.rbc-off-range-bg {
  opacity: 0.5;
  pointer-events: none;
}

/* Fix for Combobox components in modals */
.cmd-menu {
  z-index: 9999 !important;
}

[cmdk-root] {
  z-index: 9999 !important;
}

[cmdk-input] {
  z-index: 9999 !important;
}

[cmdk-list] {
  z-index: 9999 !important;
}

[cmdk-item] {
  z-index: 9999 !important;
}

.popover-content-combobox {
  z-index: 9999 !important;
}

/* Ensure dialog content is above other elements */
[role='dialog'] {
  z-index: 50 !important;
}

/* Ensure popover content is above dialog */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}
/* Add these styles at the end of the file */

/* Style for past days */
.rbc-day-bg-past {
  background-color: #f8f8f8 !important; /* Light grey background */
  opacity: 0.7;
}

/* Style for blocked days */
.rbc-day-bg-blocked {
  background-color: #ffe0e0 !important; /* Light red background */
  /* You might want a pattern or different indicator */
}

/* Style for past time slots */
.rbc-slot-past {
  background-color: #f8f8f8 !important; /* Light grey background for past slots */
  opacity: 0.7;
}

/* Style for today's background (overrides default if needed) */
.rbc-day-bg-today {
  background-color: #e6f2ff !important; /* Light blue background for today */
}

/* Ensure event clicks work even with styled backgrounds */
.rbc-day-bg {
  z-index: 0;
}
.rbc-event {
  z-index: 1; /* Ensure events are clickable above background styles */
}
