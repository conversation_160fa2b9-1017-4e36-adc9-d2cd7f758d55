import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Login from '../components/Login';
import { loginUser } from '../actions/loginCredentials';
import { useUserStore } from '../store/user.store';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

// Mock the dependencies
jest.mock('../actions/loginCredentials', () => ({
  loginUser: jest.fn()
}));

jest.mock('../store/user.store', () => ({
  useUserStore: jest.fn()
}));

jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}));

jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

describe('Login Flow Integration', () => {
  const mockSetUser = jest.fn();
  const mockPush = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the Zustand store
    (useUserStore as jest.Mock).mockReturnValue({
      setUser: mockSetUser
    });

    // Mock the router
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    });
  });

  it('completes the login flow successfully', async () => {
    // Mock successful login response
    (loginUser as jest.Mock).mockResolvedValueOnce({
      user: {
        name: 'Test User',
        email: '<EMAIL>'
      }
    });

    render(<Login />);

    // Fill in the form
    fireEvent.change(
      screen.getByPlaceholderText('Ingrese su correo electronico'),
      {
        target: { value: '<EMAIL>' }
      }
    );

    fireEvent.change(screen.getByPlaceholderText('Ingrese su contraseña'), {
      target: { value: 'password123' }
    });

    // Submit the form
    fireEvent.click(screen.getByText('Iniciar sesión', { selector: 'button' }));

    // Verify the integration flow
    await waitFor(() => {
      // Check if loginUser was called with correct data
      expect(loginUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      });

      // Check if user state was updated
      expect(mockSetUser).toHaveBeenCalledWith(
        'Test User',
        '<EMAIL>',
        ''
      );

      // Check if user was redirected to dashboard
      expect(mockPush).toHaveBeenCalledWith('/dashboard');

      // Check if success toast was displayed
      expect(toast.success).toHaveBeenCalledWith('Bienvenido Test User');
    });
  });

  it('handles login failure correctly', async () => {
    // Mock login error
    const errorMessage = 'Invalid credentials';
    (loginUser as jest.Mock).mockRejectedValueOnce(new Error(errorMessage));

    render(<Login />);

    // Fill in the form
    fireEvent.change(
      screen.getByPlaceholderText('Ingrese su correo electronico'),
      {
        target: { value: '<EMAIL>' }
      }
    );

    fireEvent.change(screen.getByPlaceholderText('Ingrese su contraseña'), {
      target: { value: 'wrong-password' }
    });

    // Submit the form
    fireEvent.click(screen.getByText('Iniciar sesión', { selector: 'button' }));

    // Verify error handling
    await waitFor(() => {
      // Check if error toast was displayed
      expect(toast.error).toHaveBeenCalledWith(errorMessage);

      // Check that user was not redirected
      expect(mockPush).not.toHaveBeenCalled();

      // Check that user state was not updated
      expect(mockSetUser).not.toHaveBeenCalled();
    });
  });
});
