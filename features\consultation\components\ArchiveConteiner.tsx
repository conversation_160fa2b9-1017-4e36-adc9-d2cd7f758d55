'use client';
import React from 'react';
import { Trash } from 'lucide-react';
import useConsultationStore from '../context/consultationStore';

const ArchiveConteiner = () => {
  const { uploadFiles, addFiles, removeFile } = useConsultationStore();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;

    if (files) {
      const newFiles = Array.from(files)
        .filter((file) => {
          if (file.size > 10 * 1024 * 1024) {
            alert(`El archivo ${file.name} supera el límite de 10MB.`);
            return false;
          }
          return true;
        })
        .map((file, index) => ({
          id: Date.now() + index,
          file
        }));
      addFiles(newFiles);
    }
  };

  return (
    <div className="mx-auto w-full rounded-sm border border-gray-300 bg-white p-4 sm:p-6">
      <h1 className="mb-2 text-sm font-semibold text-gray-800">Archivos</h1>

      <ul className="list-none space-y-2 p-0">
        {uploadFiles.map(({ id, file }) => (
          <li
            key={id}
            className="mb-2 flex w-full items-center justify-between rounded-lg border border-[#DCDBDB] p-3 text-[16px] transition-all hover:bg-gray-100"
          >
            <span className="w-full truncate">{file.name}</span>
            <button
              onClick={() => removeFile(id)}
              className="ml-3 text-[#E95454] transition-all  hover:text-red-600"
            >
              <Trash size={20} />
            </button>
          </li>
        ))}
      </ul>
      <label
        htmlFor="subirArchivos"
        className="mt-2 block w-full cursor-pointer rounded-lg border border-[#DCDBDB] bg-blue-500 p-2 text-center font-semibold text-white transition-all hover:bg-blue-700"
      >
        Subir archivos
        <input
          id="subirArchivos"
          type="file"
          accept="application/pdf"
          multiple
          onChange={handleFileChange}
          className="hidden"
        />
      </label>
    </div>
  );
};

export default ArchiveConteiner;
