import { useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { getNotifications, Notification } from '@/lib/mock-notifications';

let socket: Socket | null = null;

export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // useEffect(() => {
  //   // Initialize socket connection
  //   if (!socket) {
  //     socket = io(
  //       process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'http://localhost:3001'
  //     );
  //   }

  //   // Load initial notifications
  //   fetchNotifications();

  //   // Listen for new notifications
  //   socket.on('new-notification', (notification: Notification) => {
  //     setNotifications((prev) => [notification, ...prev]);
  //   });

  //   // Listen for notification updates
  //   socket.on('notification-read', (notificationId: string) => {
  //     setNotifications((prev) =>
  //       prev.map((n) => (n.id === notificationId ? { ...n, isRead: true } : n))
  //     );
  //   });

  //   // Listen for all notifications read
  //   socket.on('all-notifications-read', () => {
  //     setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
  //   });

  //   return () => {
  //     socket?.off('new-notification');
  //     socket?.off('notification-read');
  //     socket?.off('all-notifications-read');
  //   };
  // }, []);

  useEffect(() => {
    fetchNotifications();
  }, []);

  async function fetchNotifications() {
    try {
      // const response = await fetch('/api/notifications');
      // if (!response.ok) throw new Error('Failed to fetch notifications');
      // const data = await response.json();
      const data = await getNotifications();
      setNotifications(data);
    } catch (err) {
      setError('Failed to load notifications');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }

  async function markAsRead(id: string) {
    try {
      const response = await fetch(`/api/notifications/${id}/read`, {
        method: 'POST'
      });
      if (!response.ok) throw new Error('Failed to mark notification as read');

      // Update local state
      setNotifications((prev) =>
        prev.map((n) => (n.id === id ? { ...n, isRead: true } : n))
      );

      // Emit socket event
      socket?.emit('mark-as-read', id);
    } catch (err) {
      console.error('Failed to mark notification as read:', err);
    }
  }

  async function markAllAsRead() {
    try {
      const response = await fetch('/api/notifications/read-all', {
        method: 'POST'
      });
      if (!response.ok)
        throw new Error('Failed to mark all notifications as read');

      // Update local state
      setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));

      // Emit socket event
      socket?.emit('mark-all-as-read');
    } catch (err) {
      console.error('Failed to mark all notifications as read:', err);
    }
  }

  return {
    notifications,
    isLoading,
    error,
    markAsRead,
    markAllAsRead
  };
}
