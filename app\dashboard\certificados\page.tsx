'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { Calendar, Download, FileText, Plus, Search, User } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MedicalCertificateForm } from '@/features/certificates/components/MedicalCertificateForm';
import { toast } from 'sonner';

// Mock data for patients
const mockPatients = [
  { id: '1', name: '<PERSON>' },
  { id: '2', name: '<PERSON>' },
  { id: '3', name: '<PERSON>' },
  { id: '4', name: '<PERSON>' },
  { id: '5', name: '<PERSON>' }
];

// Mock data for doctors
const mockDoctors = [
  { id: '1', name: 'Dr<PERSON> <PERSON>' },
  { id: '2', name: 'Dra. <PERSON>' },
  { id: '3', name: 'Dr. <PERSON>' }
];

// Mock data for consultations
const mockConsultations = [
  {
    id: '1',
    patientId: '1',
    date: '2023-06-10',
    diagnosis: 'Hipertensión arterial'
  },
  {
    id: '2',
    patientId: '2',
    date: '2023-06-08',
    diagnosis: 'Faringitis aguda'
  },
  {
    id: '3',
    patientId: '3',
    date: '2023-06-05',
    diagnosis: 'Diabetes tipo 2'
  },
  {
    id: '4',
    patientId: '4',
    date: '2023-06-01',
    diagnosis: 'Migraña'
  }
];

// Mock data for certificates
const mockCertificates = [
  {
    id: '1',
    patientId: '1',
    patientName: 'Carlos López',
    doctorId: '1',
    doctorName: 'Dr. Martínez',
    date: '2023-06-10',
    certificateType: 'medical',
    certificateTypeName: 'Certificado Médico General',
    diagnosis: 'Hipertensión arterial',
    restDays: '3'
  },
  {
    id: '2',
    patientId: '2',
    patientName: 'María Fernández',
    doctorId: '2',
    doctorName: 'Dra. Sánchez',
    date: '2023-06-08',
    certificateType: 'absence',
    certificateTypeName: 'Certificado de Reposo Laboral',
    diagnosis: 'Faringitis aguda',
    restDays: '5'
  },
  {
    id: '3',
    patientId: '3',
    patientName: 'Juan Pérez',
    doctorId: '1',
    doctorName: 'Dr. Martínez',
    date: '2023-06-05',
    certificateType: 'fitness',
    certificateTypeName: 'Certificado de Aptitud Física',
    diagnosis: 'Evaluación de rutina',
    restDays: null
  },
  {
    id: '4',
    patientId: '4',
    patientName: 'Ana Rodríguez',
    doctorId: '2',
    doctorName: 'Dra. Sánchez',
    date: '2023-06-01',
    certificateType: 'absence',
    certificateTypeName: 'Certificado de Reposo Laboral',
    diagnosis: 'Migraña',
    restDays: '2'
  }
];

export default function CertificatesPage() {
  const [isCertificateFormOpen, setIsCertificateFormOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null
  );
  const [selectedConsultationId, setSelectedConsultationId] = useState<
    string | null
  >(null);
  const [selectedCertificateType, setSelectedCertificateType] = useState<
    string | null
  >(null);
  const [selectedDoctor, setSelectedDoctor] = useState<string | null>(null);

  // Filter certificates based on search term and filters
  const filteredCertificates = mockCertificates.filter((certificate) => {
    const matchesSearch =
      certificate.patientName
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      certificate.diagnosis.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesPatient =
      !selectedPatientId || certificate.patientId === selectedPatientId;

    const matchesCertificateType =
      !selectedCertificateType ||
      certificate.certificateType === selectedCertificateType;

    const matchesDoctor =
      !selectedDoctor || certificate.doctorId === selectedDoctor;

    return (
      matchesSearch && matchesPatient && matchesCertificateType && matchesDoctor
    );
  });

  // Function to handle creating a new certificate
  const handleCreateCertificate = async (data: any) => {
    console.log('Certificate data:', data);

    // Simulate API call
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        toast.success('Certificado médico generado exitosamente');
        resolve();
      }, 1000);
    });
  };

  // Function to handle certificate download
  const handleDownloadCertificate = (certificateId: string) => {
    toast.success('Descargando certificado...');
    // In a real application, this would trigger a download of the certificate
  };

  // Function to handle certificate print
  const handlePrintCertificate = (certificateId: string) => {
    toast.success('Preparando certificado para imprimir...');
    // In a real application, this would open a print dialog
  };

  return (
    <div className="space-y-4 p-4 md:p-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold">Certificados Médicos</h1>
          <p className="text-muted-foreground">
            Gestiona los certificados médicos de los pacientes
          </p>
        </div>
        <Button onClick={() => setIsCertificateFormOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Nuevo Certificado
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Buscar por paciente o diagnóstico..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <Select onValueChange={(value) => setSelectedPatientId(value)}>
          <SelectTrigger>
            <div className="flex items-center">
              <User className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Filtrar por paciente" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">Todos los pacientes</SelectItem>
            {mockPatients.map((patient) => (
              <SelectItem key={patient.id} value={patient.id}>
                {patient.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select onValueChange={(value) => setSelectedCertificateType(value)}>
          <SelectTrigger>
            <div className="flex items-center">
              <FileText className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Tipo de certificado" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">Todos los tipos</SelectItem>
            <SelectItem value="medical">Certificado Médico General</SelectItem>
            <SelectItem value="fitness">
              Certificado de Aptitud Física
            </SelectItem>
            <SelectItem value="absence">
              Certificado de Reposo Laboral
            </SelectItem>
            <SelectItem value="other">Otro</SelectItem>
          </SelectContent>
        </Select>

        <Select onValueChange={(value) => setSelectedDoctor(value)}>
          <SelectTrigger>
            <div className="flex items-center">
              <User className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Filtrar por médico" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">Todos los médicos</SelectItem>
            {mockDoctors.map((doctor) => (
              <SelectItem key={doctor.id} value={doctor.id}>
                {doctor.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredCertificates.length > 0 ? (
          filteredCertificates.map((certificate) => (
            <Card key={certificate.id}>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">
                  {certificate.patientName}
                </CardTitle>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="mr-1 h-4 w-4" />
                  {format(new Date(certificate.date), 'dd/MM/yyyy', {
                    locale: es
                  })}
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="grid gap-2">
                  <div>
                    <span className="font-medium">Tipo:</span>{' '}
                    {certificate.certificateTypeName}
                  </div>
                  <div>
                    <span className="font-medium">Médico:</span>{' '}
                    {certificate.doctorName}
                  </div>
                  <div>
                    <span className="font-medium">Diagnóstico:</span>{' '}
                    {certificate.diagnosis}
                  </div>
                  {certificate.restDays && (
                    <div>
                      <span className="font-medium">Días de reposo:</span>{' '}
                      {certificate.restDays}
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePrintCertificate(certificate.id)}
                >
                  Imprimir
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownloadCertificate(certificate.id)}
                >
                  <Download className="mr-1 h-4 w-4" />
                  Descargar
                </Button>
              </CardFooter>
            </Card>
          ))
        ) : (
          <div className="col-span-full flex h-40 items-center justify-center rounded-md border border-dashed">
            <div className="text-center">
              <p className="text-muted-foreground">
                No se encontraron certificados
              </p>
              <Button
                variant="link"
                onClick={() => {
                  setSearchTerm('');
                  setSelectedPatientId(null);
                  setSelectedCertificateType(null);
                  setSelectedDoctor(null);
                }}
              >
                Limpiar filtros
              </Button>
            </div>
          </div>
        )}
      </div>

      <MedicalCertificateForm
        isOpen={isCertificateFormOpen}
        onClose={() => {
          setIsCertificateFormOpen(false);
          setSelectedPatientId(null);
          setSelectedConsultationId(null);
        }}
        onSubmit={handleCreateCertificate}
        patients={mockPatients}
        consultations={mockConsultations}
        doctors={mockDoctors}
        initialPatientId={selectedPatientId || undefined}
        initialConsultationId={selectedConsultationId || undefined}
      />
    </div>
  );
}
