'use server';

import { api } from '../../../lib/axios';

// Define the medical event data type
export interface CreateMedicalEventData {
  patientId: string;
  physicianId: string;
  date: string;
  time: string;
  duration: number;
  reason?: string;
  notes?: string;
  diagnosis?: string;
  treatment?: string;
  // Add other fields as needed
}

// Define the response type
export interface MedicalEventResponse {
  id: string;
  patientId: string;
  physicianId: string;
  date: string;
  time: string;
  duration: number;
  reason?: string;
  notes?: string;
  diagnosis?: string;
  treatment?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Creates a new medical event (consultation)
 * @param data Medical event data
 * @returns Created medical event data
 */
export async function createMedicalEvent(
  data: CreateMedicalEventData
): Promise<MedicalEventResponse> {
  try {
    // Format the data as needed for the API
    const medicalEventData = {
      patient_id: data.patientId,
      physician_id: data.physicianId,
      date: data.date,
      time: data.time,
      duration: data.duration,
      reason: data.reason,
      notes: data.notes,
      diagnosis: data.diagnosis,
      treatment: data.treatment
    };

    // Make the API request
    const response = await api.post<MedicalEventResponse>(
      '/medical-events',
      medicalEventData
    );

    return response.data;
  } catch (error) {
    console.error('Error creating medical event:', error);

    // Handle error
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(error.message as string);
    }

    throw new Error('Failed to create medical event');
  }
}
