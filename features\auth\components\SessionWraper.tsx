'use client';
import { SessionProvider, useSession } from 'next-auth/react';
import { useUserStore } from '../store/user.store';
import { useEffect } from 'react';

function SessionSync() {
  const { data: session } = useSession();
  const setUser = useUserStore((state) => state.setUser);

  useEffect(() => {
    if (session?.user) {
      setUser(
        session.user.name || '',
        session.user.email || '',
        session.user.image || ''
      );
    }
  }, [session, setUser]);

  return null;
}

export function SessionWrapper({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <SessionSync />
      {children}
    </SessionProvider>
  );
}
