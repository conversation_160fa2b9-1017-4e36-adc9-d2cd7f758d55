'use server';

import { cookies } from 'next/headers';
import { getAuthToken } from '../features/auth/actions/auth';

/**
 * Realiza una petición a la API con autenticación
 */
export async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const token = await getAuthToken();
  const baseUrl = process.env.NEXT_PUBLIC_API_URL;
  const tenantId = cookies().get('tenant_id')?.value;
  const headers = new Headers(options.headers);

  // Establecer el tipo de contenido si no está establecido
  if (!headers.has('Content-Type')) {
    headers.set('Content-Type', 'application/json');
  }

  // Añadir el encabezado de autorización si existe el token
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }
  if (tenantId) {
    headers.set('X-Tenant-ID', tenantId);
  }

  // Log request details for debugging
  console.log('API Request:', {
    url: `${baseUrl}${endpoint}`,
    method: options.method || 'GET',
    headers: Object.fromEntries(headers.entries()),
    hasBody: !!options.body
  });

  // Create headers object with correct casing for NestJS
  const finalHeaders: Record<string, string> = {};

  // Copy all headers with proper casing
  headers.forEach((value, key) => {
    if (key.toLowerCase() === 'authorization') {
      finalHeaders['Authorization'] = value;
    } else if (key.toLowerCase() === 'x-tenant-id') {
      finalHeaders['X-Tenant-ID'] = value;
    } else if (key.toLowerCase() === 'content-type') {
      finalHeaders['Content-Type'] = value;
    } else {
      finalHeaders[key] = value;
    }
  });

  const response = await fetch(`${baseUrl}${endpoint}`, {
    ...options,
    headers: finalHeaders
  });

  if (!response.ok) {
    // Log detailed error information
    console.error('API Request failed:', {
      endpoint,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    });

    // Manejar diferentes códigos de error
    if (response.status === 401) {
      throw new Error('No autorizado. Por favor inicie sesión nuevamente.');
    }

    if (response.status === 403) {
      throw new Error('No tiene permisos para realizar esta acción.');
    }

    // Intentar obtener el mensaje de error de la respuesta
    try {
      const errorData = await response.json();
      console.error('Error response data:', errorData);

      // Handle specific error cases
      if (
        errorData.message &&
        errorData.message.includes(
          'Unique constraint failed on the fields: (`email`)'
        )
      ) {
        throw new Error(
          'Ya existe un usuario registrado con este email. Por favor, use un email diferente.'
        );
      }

      throw new Error(
        errorData.message || `Error ${response.status}: ${response.statusText}`
      );
    } catch (e) {
      // Si no podemos analizar el JSON, lanzar un error genérico
      console.error('Could not parse error response:', e);
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }
  }

  // Devolver un objeto vacío para 204 No Content
  if (response.status === 204) {
    return {} as T;
  }

  return response.json();
}

/**
 * Petición GET
 */
export async function get<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  return apiRequest<T>(endpoint, { ...options, method: 'GET' });
}

/**
 * Petición POST
 */
export async function post<T>(
  endpoint: string,
  data: any,
  options: RequestInit = {}
): Promise<T> {
  return apiRequest<T>(endpoint, {
    ...options,
    method: 'POST',
    body: JSON.stringify(data)
  });
}

/**
 * Petición PUT
 */
export async function put<T>(
  endpoint: string,
  data: any,
  options: RequestInit = {}
): Promise<T> {
  return apiRequest<T>(endpoint, {
    ...options,
    method: 'PUT',
    body: JSON.stringify(data)
  });
}

/**
 * Petición PATCH
 */
export async function patch<T>(
  endpoint: string,
  data: any,
  options: RequestInit = {}
): Promise<T> {
  return apiRequest<T>(endpoint, {
    ...options,
    method: 'PATCH',
    body: JSON.stringify(data)
  });
}

/**
 * Petición DELETE
 */
export async function del<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  return apiRequest<T>(endpoint, { ...options, method: 'DELETE' });
}
