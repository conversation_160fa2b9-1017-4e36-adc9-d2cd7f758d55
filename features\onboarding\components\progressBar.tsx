import React from 'react';

interface ProgressBarProps {
  currentStep: number;
  totalSteps: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  currentStep,
  totalSteps
}) => {
  return (
    <div className="flex items-center justify-center gap-2">
      {Array.from({ length: totalSteps }, (_, index) => (
        <div
          key={index}
          className={`h-1 w-8 rounded-md ${
            index < currentStep ? 'bg-[#487FFA]' : 'bg-gray-300'
          }`}
        ></div>
      ))}
    </div>
  );
};

export default ProgressBar;
