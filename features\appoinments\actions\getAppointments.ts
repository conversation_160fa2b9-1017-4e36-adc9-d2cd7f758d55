'use server';

import { api } from '../../../lib/axios';
import { AppointmentResponse } from './createAppointment';

// Define the pagination parameters
export interface AppointmentQueryParams {
  status?: string;
  page?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

// Define the paginated response
export interface PaginatedAppointmentsResponse {
  data: AppointmentResponse[];
  meta: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

/**
 * Fetches appointments for a user
 * @param userId User ID
 * @param params Query parameters
 * @returns Paginated appointments
 */
export async function getAppointments(
  userId: string,
  params: AppointmentQueryParams = {}
): Promise<PaginatedAppointmentsResponse> {
  try {
    // Make the API request
    const response = await api.get<PaginatedAppointmentsResponse>(
      `/appointments/${userId}`,
      { params }
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching appointments:', error);

    // Handle error
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(error.message as string);
    }

    throw new Error('Failed to fetch appointments');
  }
}

/**
 * Updates the status of an appointment
 * @param appointmentId Appointment ID
 * @param status New status
 * @param reason Reason for status change (optional)
 * @returns Updated appointment
 */
export async function updateAppointmentStatus(
  appointmentId: string,
  status: string,
  reason?: string
): Promise<AppointmentResponse> {
  try {
    // Make the API request
    const response = await api.patch<AppointmentResponse>(
      `/appointments/${appointmentId}/status`,
      { status, reason }
    );

    return response.data;
  } catch (error) {
    console.error('Error updating appointment status:', error);

    // Handle error
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(error.message as string);
    }

    throw new Error('Failed to update appointment status');
  }
}
