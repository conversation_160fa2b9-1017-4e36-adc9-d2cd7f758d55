'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const sections = [
  {
    title: 'Archivos Adjuntos',
    content: 'Aquí van los archivos adjuntos del paciente.'
  },
  { title: 'Consultas Pasadas', content: 'Listado de consultas previas.' },
  {
    title: 'Antecedentes Clínicos',
    content: 'Información sobre antecedentes médicos.'
  },
  {
    title: 'Notas de padecimiento',
    content: 'Detalles sobre padecimientos actuales.'
  },
  { title: 'Nutrición', content: 'Información nutricional relevante.' },
  {
    title: 'Exploración Física',
    content: 'Resultados de exploraciones físicas.'
  },
  { title: 'Examen Físico', content: 'Exámenes físicos realizados.' },
  {
    title: 'Diagnóstico y tratamiento',
    content: 'Diagnóstico y opciones de tratamiento.'
  }
];

const ClinicalSummary = () => {
  const [openSection, setOpenSection] = useState<string | null>(null);

  const toggleSection = (title: string) => {
    setOpenSection(openSection === title ? null : title);
  };

  return (
    <div className="mx-auto max-w-lg rounded-sm border border-gray-300 bg-white p-4">
      <h1 className="mb-2 text-sm font-semibold text-gray-800">
        Resumen Clínico
      </h1>
      <div className="space-y-2">
        {sections.map(({ title, content }) => (
          <div key={title} className="rounded-lg border">
            <button
              onClick={() => toggleSection(title)}
              className="flex w-full items-center justify-between bg-gray-100 p-3 text-right text-xs font-medium transition hover:bg-gray-200"
            >
              {openSection === title ? (
                <ChevronUp size={20} className="text-bluePrimary" />
              ) : (
                <ChevronDown size={20} className="text-bluePrimary" />
              )}
              {title}
            </button>
            {openSection === title && (
              <div className="border-t bg-gray-50 p-3 text-xs text-gray-700">
                {content}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ClinicalSummary;
