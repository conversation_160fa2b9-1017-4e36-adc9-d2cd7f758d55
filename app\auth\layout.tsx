import { Metadata } from 'next';
import LayoutDinamic from '@/features/auth/components/LayoutDinamic';

export const metadata: Metadata = {
  title: 'Authentication',
  description: 'Authentication pages'
};

export default function AuthLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <LayoutDinamic>
      <div className="w-full max-w-[500px] p-6">{children}</div>
    </LayoutDinamic>
  );
}
