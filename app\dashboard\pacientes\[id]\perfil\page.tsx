'use client';
import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Mail,
  Phone,
  Calendar,
  User,
  MapPin,
  FileText,
  Activity,
  Clock,
  Plus
} from 'lucide-react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import Avatar from '@/features/patients/assets/AvarDefault.png';
import { getPatients } from '@/features/patients/actions/getPatients';
import { DashboardPatient } from '@/features/patients/types/dashboardTypes';

const PatientProfilePage = () => {
  const { id } = useParams();
  const router = useRouter();
  const [patient, setPatient] = useState<DashboardPatient | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPatient = async () => {
      try {
        const result = await getPatients();
        const foundPatient = result.patients?.find(
          (p: DashboardPatient) => p.id === id
        );
        setPatient(foundPatient || null);
      } catch (error) {
        console.error('Error al cargar el paciente:', error);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchPatient();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Cargando perfil del paciente...</div>
      </div>
    );
  }

  if (!patient) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg text-red-600">Paciente no encontrado</div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const calculateAge = (birthdate: string) => {
    const today = new Date();
    const birth = new Date(birthdate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birth.getDate())
    ) {
      age--;
    }
    return age;
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4 text-gray-600 hover:text-gray-800"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Regresar
        </Button>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <Image
                src={Avatar}
                alt={`Foto de ${patient.name}`}
                className="h-20 w-20 rounded-lg object-cover"
              />
              <div>
                <h1 className="text-2xl font-bold text-blue-600 mb-1">
                  {patient.name}
                </h1>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <span className="flex items-center">
                    <Calendar className="mr-1 h-4 w-4" />
                    {formatDate(patient.birthdate)} -{' '}
                    {calculateAge(patient.birthdate)} años
                  </span>
                  <span className="flex items-center">
                    <Mail className="mr-1 h-4 w-4" />
                    {patient.email}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="mr-2 h-4 w-4" />
                Nueva consulta
              </Button>
              <Button variant="outline">Programar</Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Patient Info */}
        <div className="lg:col-span-1 space-y-6">
          {/* Última evolución */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">
                Última evolución
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm mb-4">Lorem ipsum dolor</p>
              <Button variant="outline" className="w-full">
                <FileText className="mr-2 h-4 w-4" />
                Ver anteriores
              </Button>
            </CardContent>
          </Card>

          {/* Antecedentes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">
                Antecedentes
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Vacunas</h4>
                <h4 className="font-medium text-sm">Alergias</h4>
                <h4 className="font-medium text-sm">
                  Antecedentes no patológicos
                </h4>
                <h4 className="font-medium text-sm">
                  Antecedentes patológicos
                </h4>
                <h4 className="font-medium text-sm">Antecedentes familiares</h4>
                <h4 className="font-medium text-sm">
                  Antecedentes quirúrgicos
                </h4>
                <h4 className="font-medium text-sm">
                  Antecedentes de la juventud
                </h4>
              </div>
            </CardContent>
          </Card>

          {/* Medicación actual */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">
                Medicación actual
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    IBUPROFENO 400MG TAB C/10
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <FileText className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <p className="text-gray-500 text-sm">Añadir droga...</p>
              </div>
            </CardContent>
          </Card>

          {/* Archivos */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Archivos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg mb-3">
                <div className="flex items-center">
                  <FileText className="mr-2 h-4 w-4 text-gray-600" />
                  <span className="text-sm">resultados_estudios.pdf</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-red-600 hover:text-red-700"
                >
                  <FileText className="h-4 w-4" />
                </Button>
              </div>
              <Button variant="outline" className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Subir archivo
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Clinical Summary */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">
                Resumen Clínico
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Próximas consultas */}
              <div className="mb-6">
                <h3 className="font-semibold mb-3">Próximas consultas</h3>
                <div className="space-y-3">
                  {[1, 2, 3, 4, 5].map((item) => (
                    <div
                      key={item}
                      className="flex items-center justify-between p-3 border-l-4 border-green-500 bg-green-50 rounded-r-lg"
                    >
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-green-700">
                            20 DIC
                          </span>
                          <span className="text-xs text-green-600">2024</span>
                        </div>
                        <p className="text-sm font-medium mt-1">
                          Dr. Tomas Vanegas
                        </p>
                        <p className="text-xs text-gray-600">
                          lorem ipsum dolor
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">10:08 AM</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-700"
                        >
                          Iniciar
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <FileText className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Separator className="my-6" />

              {/* Consultas pasadas */}
              <div>
                <h3 className="font-semibold mb-3">Consultas pasadas</h3>
                <div className="space-y-3">
                  {[1, 2, 3, 4, 5].map((item) => (
                    <div
                      key={item}
                      className="flex items-center justify-between p-3 border-l-4 border-blue-500 bg-blue-50 rounded-r-lg"
                    >
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-blue-700">
                            17 DIC
                          </span>
                          <span className="text-xs text-blue-600">2024</span>
                        </div>
                        <p className="text-sm font-medium mt-1">
                          Dr. Tomas Vanegas
                        </p>
                        <p className="text-xs text-gray-600">
                          lorem ipsum dolor
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">10:05 AM</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-700"
                        >
                          Ver consulta
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PatientProfilePage;
