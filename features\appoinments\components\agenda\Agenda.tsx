'use client';

import { useState, useEffect, useTransition, useCallback } from 'react'; // Import useTransition, useCallback
import { getCalendarAppointments } from '../../actions/getCalendarAppointments'; // Import the action
import type { AppointmentResponse } from '../../actions/createAppointment'; // Import type
import { Calendar, dateFnsLocalizer, type View } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import {
  format,
  parse,
  startOfWeek,
  getDay,
  addMonths,
  isBefore,
  isSameDay,
  parseISO // Import parseISO
} from 'date-fns';
import esES from 'date-fns/locale/es';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';

// Add custom styles for the calendar
import './calendar-styles.css';

const locales = {
  'es-ES': esES
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek: () => startOfWeek(new Date(), { weekStartsOn: 1 }),
  getDay,
  locales
});

// Define the structure for events used by react-big-calendar
interface CalendarEvent {
  title: string;
  start: Date;
  end: Date;
  resource?: AppointmentResponse; // Optional: Store original appointment data
}

// Traducción de los botones y textos del calendario
const messages = {
  today: 'Hoy',
  previous: 'Anterior',
  next: 'Siguiente',
  month: 'Mes',
  week: 'Semana',
  day: 'Día',
  date: 'Fecha',
  time: 'Hora',
  event: 'Evento',
  showMore: (total: number) => `+ Ver más (${total})`
};

interface MiniCalendarProps {
  currentDate: Date;
  onSelectDate: (date: Date) => void;
}

const MiniCalendar = ({ currentDate, onSelectDate }: MiniCalendarProps) => {
  const [displayMonth, setDisplayMonth] = useState(currentDate);

  // Update displayMonth when currentDate changes
  useEffect(() => {
    setDisplayMonth(
      new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
    );
  }, [currentDate]);

  const startOfMonth = startOfWeek(
    new Date(displayMonth.getFullYear(), displayMonth.getMonth(), 1),
    {
      weekStartsOn: 1
    }
  );
  const daysInMonth = Array.from({ length: 42 }, (_, i) => {
    const date = new Date(startOfMonth);
    date.setDate(date.getDate() + i);
    return date;
  });

  const nextMonth = () => {
    setDisplayMonth(addMonths(displayMonth, 1));
  };

  const prevMonth = () => {
    setDisplayMonth(addMonths(displayMonth, -1));
  };

  // Get today's date with time set to midnight for comparison
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return (
    <Card className="w-64 p-4">
      <div className="mb-4 flex items-center justify-between">
        <button onClick={prevMonth}>&lt;</button>
        <span className="font-semibold text-bluePrimary">
          {format(displayMonth, 'MMMM yyyy', { locale: esES })}
        </span>
        <button onClick={nextMonth}>&gt;</button>
      </div>
      <div className="grid grid-cols-7 gap-1 text-sm">
        {['L', 'M', 'X', 'J', 'V', 'S', 'D'].map((day) => (
          <div
            key={day}
            className="text-center font-medium text-muted-foreground"
          >
            {day}
          </div>
        ))}
        {daysInMonth.map((date, i) => {
          const isPastDate = isBefore(date, today);
          return (
            <button
              key={i}
              onClick={() => !isPastDate && onSelectDate(date)}
              className={cn(
                'h-8 w-8 rounded-md text-center text-sm',
                date.getMonth() === displayMonth.getMonth()
                  ? 'text-foreground'
                  : 'text-muted-foreground',
                format(date, 'yyyy-MM-dd') === format(currentDate, 'yyyy-MM-dd')
                  ? 'border-[1px] border-bluePrimary bg-blue-200 text-bluePrimary'
                  : '',
                isPastDate
                  ? 'cursor-not-allowed opacity-50'
                  : 'hover:bg-blue-100'
              )}
              disabled={isPastDate}
            >
              {format(date, 'd')}
            </button>
          );
        })}
      </div>
    </Card>
  );
};

interface AgendaProps {
  onDateSelect?: (date: Date) => void;
}

const Agenda = ({ onDateSelect }: AgendaProps) => {
  // Initialize with a date in March 2025
  const [currentView, setCurrentView] = useState<View>('week');
  const [currentDate, setCurrentDate] = useState(new Date(2025, 3, 7)); // April 7, 2025 (Month is 0-indexed)

  // State for calendar data
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);
  const [blockedDays, setBlockedDays] = useState<string[]>([]); // Store as "YYYY-MM-DD"
  const [isLoadingCalendar, startCalendarTransition] = useTransition();
  const [calendarError, setCalendarError] = useState<string | null>(null);

  const handleViewChange = (view: View) => {
    setCurrentView(view);
  };

  const handleNavigate = (date: Date) => {
    setCurrentDate(date);
  };

  const handleSelectSlot = (slotInfo: {
    start: Date;
    end: Date;
    action: string;
  }) => {
    // Get today's date with time set to midnight for comparison
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Only allow selection of dates that are not in the past
    if (onDateSelect && !isBefore(slotInfo.start, today)) {
      onDateSelect(slotInfo.start);
    }
  };

  const handleSelectDate = (date: Date) => {
    // Update the current date in the main calendar
    setCurrentDate(date);

    // If we're in week or day view, we want to maintain that view
    // If we're in month view and select a date, switch to day view
    if (currentView === 'month') {
      setCurrentView('day');
    }
  };
  // Fetch calendar data when the date (month/year) changes
  useEffect(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1; // API expects 1-based month

    setCalendarError(null); // Clear previous errors
    startCalendarTransition(async () => {
      try {
        const data = await getCalendarAppointments(month, year);

        // Map API response to CalendarEvent format
        const formattedEvents: CalendarEvent[] = data.appointments.map(
          (appt) => ({
            title: appt.consultation_reason || 'Cita Programada', // Use reason or default title
            start: parseISO(appt.start), // Convert ISO string to Date
            end: parseISO(appt.end), // Convert ISO string to Date
            resource: appt // Store original data
          })
        );

        setCalendarEvents(formattedEvents);
        setBlockedDays(data.blockedDays || []); // Store blocked days (YYYY-MM-DD)
      } catch (error) {
        console.error('Error fetching calendar data:', error);
        setCalendarError(
          error instanceof Error ? error.message : 'Error al cargar la agenda.'
        );
        setCalendarEvents([]); // Clear events on error
        setBlockedDays([]);
      }
    });
  }, [currentDate]); // Re-fetch when currentDate changes

  // Style days (e.g., disable past/blocked days)
  const dayPropGetter = useCallback(
    (date: Date) => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const formattedDate = format(date, 'yyyy-MM-dd');

      const isPast = isBefore(date, today);
      const isBlocked = blockedDays.includes(formattedDate);

      let className = '';
      if (isPast) {
        className = 'rbc-day-bg-past'; // Add custom class for past days
      }
      if (isBlocked) {
        className = `${className} rbc-day-bg-blocked`; // Add custom class for blocked days
      }
      // Highlight today
      if (isSameDay(date, today)) {
        className = `${className} rbc-day-bg-today`;
      }

      return {
        className: className.trim(),
        style: {
          cursor: isPast || isBlocked ? 'not-allowed' : 'pointer'
          // Add more specific background/opacity styles via CSS using the classes
        }
      };
    },
    [blockedDays]
  );

  // Style time slots (e.g., disable past slots) - more complex, basic past day disabling is in dayPropGetter
  const slotPropGetter = useCallback((date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (isBefore(date, today)) {
      return {
        className: 'rbc-slot-past', // Custom class for past slots
        style: {
          cursor: 'not-allowed'
          // backgroundColor: '#f0f0f0', // Example styling
        }
      };
    }
    return {};
  }, []);

  return (
    <div className="flex h-full w-full gap-4">
      <div className="flex-1">
        <Calendar
          localizer={localizer}
          events={calendarEvents} // Use state for events
          startAccessor="start"
          endAccessor="end"
          views={['day', 'week', 'month']}
          view={currentView}
          date={currentDate}
          onView={handleViewChange}
          onNavigate={handleNavigate}
          messages={messages}
          culture="es-ES"
          className="h-[calc(100vh-12rem)] bg-white"
          min={new Date(2025, 0, 1, 8, 0)} // 8:00 AM
          max={new Date(2025, 0, 1, 18, 0)} // 6:00 PM
          onSelectSlot={handleSelectSlot}
          selectable
          dayPropGetter={dayPropGetter} // Apply day styling
          slotPropGetter={slotPropGetter} // Apply slot styling
        />
      </div>
      <div className="flex-none">
        <MiniCalendar
          currentDate={currentDate}
          onSelectDate={handleSelectDate}
        />
      </div>
    </div>
  );
};

export default Agenda;
