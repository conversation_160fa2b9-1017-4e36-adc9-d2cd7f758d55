'use client';
import React, { useState } from 'react';
import { Trash } from 'lucide-react';
import useConsultationStore from '../../context/consultationStore';

const Diagnosis = () => {
  const [diagnosticInput, setDiagnosticInput] = useState('');
  const { diagnoses, addDiagnosis, removeDiagnosis, updateDiagnosisComment } =
    useConsultationStore();
  const [loading, setLoading] = useState(false);

  const saveToBackend = async (diagnosis: string) => {
    try {
      setLoading(true);
      const response = await fetch('/api/diagnoses', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ diagnosis })
      });

      if (!response.ok) {
        throw new Error('Error al guardar el procedimiento');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error al enviar los datos:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveCommentToBackend = async (diagnosis: string, comment: string) => {
    try {
      const response = await fetch('/api/diagnoses/comments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ diagnosis, comment })
      });

      if (!response.ok) {
        throw new Error('Error al guardar el comentario');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error al enviar el comentario:', error);
    }
  };

  const handleDiagnosisBlur = async () => {
    if (diagnosticInput.trim() !== '') {
      addDiagnosis(diagnosticInput);
      await saveToBackend(diagnosticInput);
      setDiagnosticInput('');
    }
  };

  const handleCommentBlur = async (diagnosis: string, comment: string) => {
    await saveCommentToBackend(diagnosis, comment);
  };

  return (
    <div className="mx-auto w-full max-w-2xl rounded-lg border border-gray-300 bg-white  p-4">
      <h1 className="mb-2 text-sm font-semibold text-gray-800">Diagnósticos</h1>
      <div className="mb-4 flex gap-2">
        <input
          type="text"
          placeholder="Buscar Diagnóstico..."
          className="w-full rounded-lg border border-gray-300 bg-white p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
          value={diagnosticInput}
          onChange={(e) => setDiagnosticInput(e.target.value)}
          onBlur={handleDiagnosisBlur}
        />
        <button
          className="rounded bg-blue-500 px-2 py-2 text-white"
          onClick={handleDiagnosisBlur}
          disabled={loading}
        >
          {loading ? 'Guardando...' : 'Agregar'}
        </button>
      </div>
      <div className="flex flex-col gap-3 rounded-lg border border-gray-300 bg-gray-50 p-3">
        {Object.entries(diagnoses).map(([diagnostic, comment]) => (
          <div
            key={diagnostic}
            className="flex flex-col justify-between rounded-lg bg-white p-4 shadow-sm transition-all hover:bg-gray-100"
          >
            <div className="mb-2 flex items-center justify-between">
              <p className="font-semibold text-gray-700">{diagnostic}</p>
              <button
                className="text-red-500 transition-all hover:text-red-600"
                onClick={() => removeDiagnosis(diagnostic)}
              >
                <Trash size={20} />
              </button>
            </div>
            <input
              type="text"
              placeholder="Agregar comentario"
              className="h-[60px] w-full rounded-lg border border-gray-300 bg-white p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
              value={comment}
              onChange={(e) =>
                updateDiagnosisComment(diagnostic, e.target.value)
              }
              onBlur={() => handleCommentBlur(diagnostic, comment)}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default Diagnosis;
