'use client';

import { useState, useEffect } from 'react';
import { ConsultationForm } from '@/features/consultations/components/ConsultationForm';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import { createMedicalEvent } from '@/features/medical-events/actions/createMedicalEvent';
import GetPatients from '@/features/patients/actions/getPatient';
import { Loader2 } from 'lucide-react';

// Mock data for patients
const mockPatients = [
  { id: '1', name: '<PERSON>' },
  { id: '2', name: '<PERSON>' },
  { id: '3', name: '<PERSON>' },
  { id: '4', name: '<PERSON>' },
  { id: '5', name: '<PERSON>' }
];

// Mock data for appointments
const mockAppointments = [
  {
    id: '1',
    patientId: '1',
    patientName: '<PERSON>',
    date: '2025-03-15',
    time: '09:00',
    status: 'Confirmada',
    doctorId: '1'
  },
  {
    id: '2',
    patientId: '2',
    patientName: '<PERSON>',
    date: '2025-03-15',
    time: '10:30',
    status: 'Confirmada',
    doctorId: '2'
  },
  {
    id: '3',
    patientId: '3',
    patientName: '<PERSON>',
    date: '2025-03-16',
    time: '11:00',
    status: 'Pendiente',
    doctorId: '1'
  },
  {
    id: '4',
    patientId: '4',
    patientName: 'Ana Rodríguez',
    date: '2025-03-17',
    time: '14:00',
    status: 'Confirmada',
    doctorId: '1'
  }
];

export default function NewConsultationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const patientId = searchParams.get('patientId');
  const appointmentId = searchParams.get('appointmentId');
  const [patients, setPatients] = useState<any[]>([]);

  // Fetch patients data
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch patients
        const patientsData = await GetPatients();
        // If result is an array (mock data), use it directly
        // If it's an object with data property (API response), use the data property
        setPatients(
          Array.isArray(patientsData) ? patientsData : patientsData.data || []
        );
      } catch (error) {
        console.error('Error fetching patients:', error);
        toast.error('Error al cargar los datos');
      }
    };

    fetchData();
  }, []);

  const handleCreateConsultation = async (data: any) => {
    try {
      // Format the data for the API
      const medicalEventData = {
        patientId: data.patientId,
        physicianId: data.doctorId || '1', // Default doctor ID if not provided
        date: data.date,
        time: data.time,
        duration: data.duration || 30, // Default to 30 minutes if not specified
        reason: data.reason,
        notes: data.notes,
        diagnosis: data.diagnosis,
        treatment: data.treatment
      };

      // Call the API
      await createMedicalEvent(medicalEventData);

      toast.success('Consulta registrada exitosamente');

      // Redirect to consultations list
      router.push('/dashboard/consultas');
    } catch (error) {
      console.error('Error creating consultation:', error);
      toast.error('Error al registrar la consulta');
    }
  };

  return (
    <ConsultationForm
      onSubmit={handleCreateConsultation}
      patients={mockPatients}
      appointments={mockAppointments}
      initialPatientId={patientId || undefined}
      initialAppointmentId={appointmentId || undefined}
      onCancel={() => router.push('/dashboard/consultas')}
    />
  );
}
