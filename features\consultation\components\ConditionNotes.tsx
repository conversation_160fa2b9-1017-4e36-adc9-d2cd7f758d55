'use client';
import React from 'react';
import VitalSings from './VitalSings';
import Archive<PERSON>onteiner from './ArchiveConteiner';
import ConsultNotes from './ConsultNotes';
import ClinicalSummary from './ClinicalSummary';

const ConditionNotes = () => {
  return (
    <div className="flex flex-col p-4">
      <h1 className="mb-4 text-lg font-semibold text-gray-800">
        Notas de padecimiento
      </h1>
      <div className="grid h-full grid-cols-1 gap-4 md:grid-cols-[1fr_2fr_1fr]">
        <div className="flex h-full flex-col items-start justify-start gap-4">
          <VitalSings />
        </div>

        <div className="flex flex-col items-start justify-start gap-4">
          <ArchiveConteiner />
          <ConsultNotes />
        </div>

        <div className="h-full min-h-[400px]">
          <ClinicalSummary />
        </div>
      </div>
    </div>
  );
};

export default ConditionNotes;
