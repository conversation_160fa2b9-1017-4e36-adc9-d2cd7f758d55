// Basic patient information
export interface Patient {
  id: string;
  name: string;
  identification_type: string;
  identification_number: string;
  age: number;
  main_diagnostic_cie: string;
  email?: string;
  phone?: string;
}

// Appointment history
export interface AppointmentHistory {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  reason: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  doctorName: string;
  notes?: string;
}

// Vital signs
export interface VitalSigns {
  id: string;
  date: string;
  temperature?: number; // in Celsius
  bloodPressureSystolic?: number; // in mmHg
  bloodPressureDiastolic?: number; // in mmHg
  heartRate?: number; // in BPM
  respiratoryRate?: number; // in breaths per minute
  oxygenSaturation?: number; // in percentage
  weight?: number; // in kg
  height?: number; // in cm
  bmi?: number; // calculated from weight and height
  notes?: string;
}

// Studies (lab tests, imaging, etc.)
export interface Study {
  id: string;
  date: string;
  type: string; // e.g., "Blood Test", "X-Ray", "MRI"
  description: string;
  requestedBy: string; // doctor name
  results?: string;
  fileUrls?: string[]; // URLs to study files/images
  notes?: string;
}

// Progress notes
export interface ProgressNote {
  id: string;
  date: string;
  doctorName: string;
  content: string;
}

// Physical examination
export interface PhysicalExamination {
  id: string;
  date: string;
  generalAppearance?: string;
  skin?: string;
  head?: string;
  eyes?: string;
  ears?: string;
  nose?: string;
  mouth?: string;
  neck?: string;
  chest?: string;
  lungs?: string;
  heart?: string;
  abdomen?: string;
  extremities?: string;
  neurologicalExam?: string;
  notes?: string;
}

// Diagnosis
export interface Diagnosis {
  id: string;
  date: string;
  description: string;
  icdCode?: string; // International Classification of Diseases code
  type: 'primary' | 'secondary';
  status: 'active' | 'resolved' | 'recurrent';
  notes?: string;
}

// Lab/Radiology requests
export interface LabRadiologyRequest {
  id: string;
  date: string;
  type: 'laboratory' | 'radiology' | 'other';
  description: string;
  status: 'requested' | 'completed' | 'cancelled';
  results?: string;
  fileUrls?: string[];
  notes?: string;
}

// Medical procedures
export interface Procedure {
  id: string;
  date: string;
  name: string;
  description: string;
  performedBy: string;
  location?: string;
  notes?: string;
}

// Medication
export interface Medication {
  id: string;
  name: string;
  dosage: string;
  route: string; // e.g., "oral", "intravenous"
  frequency: string;
  startDate: string;
  endDate?: string;
  status: 'active' | 'discontinued' | 'completed';
  prescribedBy: string;
  notes?: string;
}

// Medical instruction
export interface MedicalInstruction {
  id: string;
  date: string;
  content: string;
  doctorName: string;
}

// Medical treatment
export interface MedicalTreatment {
  id: string;
  date: string;
  description: string;
  duration?: string;
  notes?: string;
}

// Vaccination
export interface Vaccination {
  id: string;
  date: string;
  name: string;
  manufacturer?: string;
  lotNumber?: string;
  administeredBy?: string;
  site?: string; // e.g., "left arm"
  notes?: string;
}

// Allergy
export interface Allergy {
  id: string;
  allergen: string;
  reaction: string;
  severity: 'mild' | 'moderate' | 'severe';
  dateIdentified: string;
  status: 'active' | 'inactive';
  notes?: string;
}

// Past medical history
export interface PastMedicalHistory {
  id: string;
  condition: string;
  diagnosisDate?: string;
  resolutionDate?: string;
  status: 'resolved' | 'ongoing';
  notes?: string;
}

// Non-pathological history (lifestyle)
export interface NonPathologicalHistory {
  id: string;
  category: 'diet' | 'exercise' | 'smoking' | 'alcohol' | 'drugs' | 'other';
  description: string;
  notes?: string;
}

// Family history
export interface FamilyHistory {
  id: string;
  condition: string;
  relationship: string; // e.g., "mother", "father", "sibling"
  notes?: string;
}

// Surgical history
export interface SurgicalHistory {
  id: string;
  procedure: string;
  date: string;
  surgeon?: string;
  hospital?: string;
  notes?: string;
}

// Youth history
export interface YouthHistory {
  id: string;
  category: 'development' | 'childhood_illness' | 'injury' | 'other';
  description: string;
  age?: string;
  notes?: string;
}

// Complete clinical history
export interface ClinicalHistory {
  patientId: string;
  appointments: AppointmentHistory[];
  vitalSigns: VitalSigns[];
  studies: Study[];
  progressNotes: ProgressNote[];
  physicalExaminations: PhysicalExamination[];
  diagnoses: Diagnosis[];
  labRadiologyRequests: LabRadiologyRequest[];
  procedures: Procedure[];
  medications: Medication[];
  medicalInstructions: MedicalInstruction[];
  medicalTreatments: MedicalTreatment[];
  vaccinations: Vaccination[];
  allergies: Allergy[];
  pastMedicalHistory: PastMedicalHistory[];
  nonPathologicalHistory: NonPathologicalHistory[];
  familyHistory: FamilyHistory[];
  surgicalHistory: SurgicalHistory[];
  youthHistory: YouthHistory[];
}
