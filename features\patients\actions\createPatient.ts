'use server';

import { z } from 'zod';
import { post } from '@/lib/api'; // Corrected path alias
import { MedicalPatientDto } from '../types/patient'; // Added import

// Define the schema for the nested structure matching the component's payload
const userSchema = z.object({
  name: z.string().min(2, 'El nombre debe tener al menos 2 caracteres'),
  last_name: z.string().min(2, 'El apellido debe tener al menos 2 caracteres'),
  email: z.string().email('Correo electrónico inválido'),
  identification_type: z
    .string()
    .min(1, 'El tipo de identificación es requerido'),
  identification_number: z
    .string()
    .min(7, 'El número de identificación debe tener al menos 7 caracteres')
    .max(15),
  birth_date: z.string().min(1, 'La fecha de nacimiento es requerida'),
  nationality: z.string().min(1, 'La nacionalidad es requerida'),
  gender: z.string().min(1, 'El género es requerido'),
  phone_prefix: z.string().min(1, 'El prefijo telefónico es requerido'),
  phone: z.string().min(8, 'El teléfono debe tener al menos 8 dígitos'),
  image: z.string().url('Debe ser una URL válida').optional() // Only allow valid URLs, make optional
});

const patientDetailsSchema = z.object({
  direction: z.string().min(1, 'La dirección es requerida'),
  country: z.string().min(1, 'El país es requerido'),
  province: z.string().min(1, 'La provincia es requerida'),
  city: z.string().min(1, 'La ciudad es requerida'),
  postal_code: z.string().min(1, 'El código postal es requerido'),
  direction_number: z.string().nullable().optional(),
  apartment: z.string().nullable().optional(),
  health_care_number: z.string().nullable().optional()
});

const createPatientPayloadSchema = z.object({
  user: userSchema,
  patient: patientDetailsSchema
  // tenant_id might be added here if needed at the root by the backend
  // If tenant_id is required by the backend at the root level, add it here:
  // tenant_id: z.string().default('1') // Example if needed
});

export type CreatePatientPayload = z.infer<typeof createPatientPayloadSchema>;

export async function createPatient(payload: CreatePatientPayload) {
  try {
    console.log('--- Original Payload Received ---');
    console.log(JSON.stringify(payload, null, 2));
    console.log('--------------------------------');

    // Clean the payload before validation
    const cleanedPayload = {
      user: {
        name: payload.user.name,
        last_name: payload.user.last_name,
        email: payload.user.email,
        identification_type: payload.user.identification_type,
        identification_number: payload.user.identification_number,
        birth_date: payload.user.birth_date ? `${payload.user.birth_date}T00:00:00.000Z` : undefined,
        nationality: payload.user.nationality,
        gender: payload.user.gender,
        phone_prefix: payload.user.phone_prefix,
        phone: payload.user.phone,
        // Only include image if it's a valid URL
        ...(payload.user.image && payload.user.image.trim() !== ''
          ? { image: payload.user.image }
          : {})
      },
      patient: {
        direction: payload.patient.direction,
        country: payload.patient.country,
        province: payload.patient.province,
        city: payload.patient.city,
        postal_code: payload.patient.postal_code,
        direction_number: payload.patient.direction_number || null,
        apartment: payload.patient.apartment || null,
        health_care_number: payload.patient.health_care_number || null
      }
    };

    console.log('--- Cleaned Payload ---');
    console.log(JSON.stringify(cleanedPayload, null, 2));
    console.log('----------------------');

    // Validate the cleaned payload
    const validatedPayload = createPatientPayloadSchema.parse(cleanedPayload);

    console.log('--- Validated Payload ---');
    console.log(JSON.stringify(validatedPayload, null, 2));
    console.log('-------------------------');

    // Send to API endpoint
    return await post<MedicalPatientDto>('/patient', validatedPayload);
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error in createPatient action:', error.errors);
      throw new Error(
        `Validation failed: ${error.errors
          .map((e) => `${e.path.join('.')} - ${e.message}`)
          .join(', ')}`
      );
    }
    console.error('Error creating patient:', error);
    throw error;
  }
}
