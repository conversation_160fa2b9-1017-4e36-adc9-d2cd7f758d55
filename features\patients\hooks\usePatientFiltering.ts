import { useMemo } from 'react';
import {
  SortCriteria,
  SortDirection,
  DashboardPatient
} from '../types/dashboardTypes';

interface UsePatientFilteringProps {
  patients: DashboardPatient[];
  searchTerm: string;
  sortBy: SortCriteria;
  sortDirection: SortDirection;
}

/**
 * Custom hook to handle filtering and sorting of patients
 */
export function usePatientFiltering({
  patients,
  searchTerm,
  sortBy,
  sortDirection
}: UsePatientFilteringProps) {
  // Filter and sort patients based on search term and sort criteria
  const filteredAndSortedPatients = useMemo(() => {
    // First filter by search term
    const filtered = patients.filter((patient) =>
      patient.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Then sort based on the selected criteria
    return [...filtered].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'age-asc':
          // Convert birthdate strings to Date objects for comparison
          const dateA = new Date(a.birthdate);
          const dateB = new Date(b.birthdate);
          comparison = dateB.getTime() - dateA.getTime(); // Older people (earlier birthdates) first
          break;
        case 'age-desc':
          // Convert birthdate strings to Date objects for comparison
          const dateC = new Date(a.birthdate);
          const dateD = new Date(b.birthdate);
          comparison = dateC.getTime() - dateD.getTime(); // Younger people (later birthdates) first
          break;
        case 'recent':
          // Sort by ID in descending order (higher IDs are more recent)
          // Convert to numbers for proper comparison
          const idA = parseInt(a.id) || 0;
          const idB = parseInt(b.id) || 0;
          comparison = idB - idA; // Higher IDs first (more recent)
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }

      // For 'recent' sorting, always show most recent first (ignore sortDirection)
      if (sortBy === 'recent') {
        return comparison;
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [patients, searchTerm, sortBy, sortDirection]);

  return { filteredAndSortedPatients };
}
