import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(req: NextRequest) {
  //! VERIFICAR DONDE ESTARA EL TOKEN
  const userData = req.cookies.get('next-auth.session-token');

  if (!userData) {
    // return NextResponse.redirect(new URL('/auth/login', req.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/dashboard/:path*', '/']
};
