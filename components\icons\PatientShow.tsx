import React from 'react';

const PatientShow = () => {
  return (
    <div>
      <svg
        width="159.917969"
        height="159.917969"
        viewBox="0 0 159.918 159.918"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <ellipse
          id="SVGID_00000085235915349724696250000005668314038032623511_"
          rx="56.539463"
          ry="56.539455"
          transform="matrix(0.707107 -0.707107 0.707107 0.707107 79.959 79.959)"
          fill="#F7D8BA"
          fillOpacity="1.000000"
        />
        <mask
          id="mask4538_2854"
          mask-type="alpha"
          maskUnits="userSpaceOnUse"
          x="0.000000"
          y="0.000000"
          width="159.917969"
          height="159.917969"
        >
          <ellipse
            id="SVGID_00000085235915349724696250000005668314038032623511_"
            rx="56.539463"
            ry="56.539455"
            transform="matrix(0.707107 -0.707107 0.707107 0.707107 79.959 79.959)"
            fill="#000000"
            fillOpacity="1.000000"
          />
        </mask>
        <g mask="url(#mask4538_2854)">
          <path
            id="path"
            d="M135.4 139.5L26.31 139.5C31.34 116.92 44.84 112.41 44.84 112.41L70.86 100.24L80.72 100.15L80.99 100.15L90.86 100.24L116.88 112.41C116.88 112.41 130.37 116.92 135.4 139.5Z"
            fill="#FFFFFF"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M86.81 35.37C86.81 35.37 95.89 31.33 99.11 38.98C102.21 46.34 89.9 53.36 89.9 53.36L86.81 35.37Z"
            fill="#1E0B02"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M69.83 65.91C69.93 69.31 67.25 72.14 63.85 72.24C60.45 72.33 57.61 69.66 57.52 66.25C57.42 62.85 60.1 60.02 63.5 59.92C66.9 59.83 69.74 62.5 69.83 65.91Z"
            fill="#EEAC9A"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M60.35 65.52L60.34 65.55C60.15 65.64 59.93 65.57 59.84 65.38C59.75 65.19 59.82 64.97 60.01 64.88L60.04 64.89L60.35 65.52ZM65.26 68.09L65.29 68.1C65.39 68.28 65.32 68.5 65.14 68.61C64.96 68.71 64.73 68.64 64.63 68.46L64.64 68.43L65.26 68.09Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M60.17 65.21C60.17 65.21 62.62 64.02 64.96 68.28"
            stroke="#A8523F"
            strokeOpacity="1.000000"
            strokeWidth="0.748200"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <ellipse
            id="circle"
            cx="98.492188"
            cy="66.084473"
            rx="6.161011"
            ry="6.161003"
            fill="#EEAC9A"
            fillOpacity="1.000000"
          />
          <path
            id="path"
            d="M102.09 64.78L102.11 64.77C102.31 64.86 102.39 65.08 102.3 65.27C102.21 65.46 102 65.54 101.81 65.46L101.8 65.43L102.09 64.78ZM97.59 68.46L97.6 68.48C97.5 68.67 97.28 68.74 97.09 68.64C96.91 68.55 96.83 68.33 96.93 68.14L96.96 68.13L97.59 68.46Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M101.96 65.12C101.96 65.12 99.48 63.99 97.26 68.31"
            stroke="#A8523F"
            strokeOpacity="1.000000"
            strokeWidth="0.748200"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M88.98 80.28C88.98 80.28 85.51 82.73 82.35 83.33C79.18 83.94 76.1 82.1 76.06 82.08C76.02 82.06 72.37 79.95 72.37 79.95L70.85 100.23L71.26 100.57C76.63 105 84.37 105.05 89.79 100.69L90.59 100.06L88.98 80.28Z"
            fill="#EEAC9A"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M70.85 77.82C70.85 77.82 74.31 89.12 82.25 88.99C88.78 88.89 89.83 77.82 89.83 77.82L70.85 77.82Z"
            fill="#DD9987"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M97.72 55.35C97.75 62.32 96.79 68.33 95.82 72.6C94.87 76.78 92.14 79.72 88.29 82.08L87.59 82.52C83.51 85.02 78.22 85.11 74.06 82.73C69.86 80.34 66.91 77.17 65.98 72.69C64.3 64.55 64.07 55.52 64.07 55.52C64.07 55.52 61.71 36.69 79.85 36.94C98 37.19 97.72 55.35 97.72 55.35Z"
            fill="#EEAC9A"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <ellipse
            id="circle"
            cx="73.073242"
            cy="60.883789"
            rx="1.467987"
            ry="1.468002"
            fill="#1E0B02"
            fillOpacity="1.000000"
          />
          <ellipse
            id="circle"
            cx="87.479492"
            cy="60.883789"
            rx="1.467987"
            ry="1.468002"
            fill="#1E0B02"
            fillOpacity="1.000000"
          />
          <path
            id="path"
            d="M67.6 54.67L77.19 53.81C77.19 53.81 76.33 51.32 72.46 51.73C68.49 52.14 67.6 54.67 67.6 54.67Z"
            fill="#000000"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M92.33 54.67L82.73 53.81C82.73 53.81 83.59 51.32 87.47 51.73C91.44 52.14 92.33 54.67 92.33 54.67Z"
            fill="#000000"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M75.66 70.43L85.12 70.43C85.12 70.43 84.67 73.33 80.25 73.33C75.82 73.33 75.66 70.43 75.66 70.43Z"
            fill="#FFFFFF"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M64.69 64.44C64.69 64.44 67.38 44.06 66.06 40.71C66.06 40.71 56.93 48.27 64.69 64.44Z"
            fill="#1E0B02"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M97.03 64.28C97.03 64.28 94.01 44.82 94.85 39.22C94.85 39.22 103.67 47.04 97.03 64.28Z"
            fill="#1E0B02"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M94.22 37.83L85.85 36.57L85.11 35.12L80.7 35.79L76.28 35.12L75.55 36.57L67.18 37.83L66.06 42.31L66.34 47.47C66.34 47.47 67.66 41.62 72.27 42.78C73.36 43.06 74.66 43.51 76.18 43.82C77.6 44.13 78.96 44.27 80.24 44.29C80.39 44.3 80.54 44.29 80.7 44.29C80.85 44.29 81.01 44.3 81.16 44.29C82.44 44.27 83.79 44.13 85.21 43.82C86.73 43.51 88.06 43.15 89.12 42.78C93.39 41.3 94.97 47.2 94.97 47.2L95.33 42.31L94.22 37.83Z"
            fill="#1E0B02"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M94.05 42.93C94.05 42.93 88.61 47.78 81.64 44.05C81.64 44.05 63.9 54.98 58.9 43.08C54.72 33.17 72.05 19.71 89.92 34.87L94.05 42.93Z"
            fill="#1E0B02"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M70.85 100.23L65.04 102.9C65.04 102.9 64.98 112.65 80.77 112.14C96.97 111.62 96.87 103.05 96.87 103.05L90.59 100.06L70.85 100.23Z"
            fill="#EEAC9A"
            fillOpacity="1.000000"
            fillRule="nonzero"
          />
        </g>
      </svg>
    </div>
  );
};

export default PatientShow;
