'use server';

import { ForgotSchema, ForgotSchemaType } from '../lib/zod/schema';
import {
  AuthError,
  ResetPasswordResponse,
  isValidationError
} from '../lib/types';
import { post } from '../../../lib/api';

export async function resetPassword(
  data: ForgotSchemaType
): Promise<{ message: string }> {
  try {
    // Validate data with Zod
    const validatedData = ForgotSchema.safeParse(data);

    if (!validatedData.success) {
      throw {
        message: validatedData.error.errors[0].message,
        status: 400,
        code: 'VALIDATION_ERROR'
      } as AuthError;
    }

    // Make API request using the new API client
    const responseData = await post<ResetPasswordResponse>(
      '/auth/request-password',
      validatedData.data
    );

    return {
      message: responseData.message || 'Email with instructions has been sent'
    };
  } catch (error) {
    // If it's a Zod validation error
    if (isValidationError(error)) {
      throw {
        message: error.errors[0].message,
        status: 400,
        code: 'VALIDATION_ERROR'
      } as AuthError;
    }

    // If it's an API error
    if (error && typeof error === 'object' && 'message' in error) {
      throw {
        message: (error.message as string) || 'Password recovery error',
        status: 400,
        code: 'RESET_PASSWORD_ERROR'
      } as AuthError;
    }

    // Generic error
    throw {
      message:
        error instanceof Error ? error.message : 'Password recovery error',
      status: 500,
      code: 'UNKNOWN_ERROR'
    } as AuthError;
  }
}
