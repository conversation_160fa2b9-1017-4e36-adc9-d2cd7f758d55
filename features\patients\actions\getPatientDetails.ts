'use server';

import { get } from '@/lib/api';
import { MedicalPatientDto } from '@/features/patients/types/patient';

export async function getPatientDetails(
  patientId: string
): Promise<MedicalPatientDto> {
  try {
    // TODO: Add permission check if necessary for viewing details

    // Fetch patient details using the generic 'get' function
    // Assuming the API returns the full patient DTO matching MedicalPatientDto
    const patientDetails = await get<MedicalPatientDto>(
      `/patient/${patientId}`
    );

    return patientDetails;
  } catch (error) {
    console.error(`Error fetching details for patient ${patientId}:`, error);
    // Re-throw the error to be handled by the calling component
    // Consider returning a specific error structure if needed
    throw error;
  }
}
