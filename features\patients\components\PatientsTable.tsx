'use client';

import Image from 'next/image';
import Avatar from '@/features/patients/assets/AvarDefault.png';
// Removed Table components import - using native HTML table for better layout control
import { PatientActions } from './PatientActions';
import { PatientTableProps } from '../types/dashboardTypes';
import { useRouter } from 'next/navigation';

export function PatientsTable({
  patients,
  onViewDetails,
  onViewClinicalHistory,
  onEditPatient,
  onDeletePatient
}: PatientTableProps) {
  const router = useRouter();

  const handlePatientClick = (patientId: string) => {
    router.push(`/dashboard/pacientes/${patientId}/perfil`);
  };

  return (
    <div className="w-full rounded-md border border-gray-200 bg-white h-full flex flex-col">
      <div className="flex-1 overflow-auto">
        <table className="w-full border-collapse">
          <thead className="sticky top-0 bg-white">
            <tr className="border-b border-gray-200">
              <th className="py-4 px-6 text-left font-medium text-gray-500">
                Nombre
              </th>
              <th className="py-4 px-6 text-left font-medium text-gray-500">
                Edad
              </th>
              <th className="py-4 px-6 text-left font-medium text-gray-500">
                Diagnóstico
              </th>
              <th className="py-4 px-6 text-left font-medium text-gray-500">
                Email
              </th>
              <th className="py-4 px-6 text-left font-medium text-gray-500">
                Teléfono
              </th>
              <th className="py-4 px-6 text-center font-medium text-gray-500"></th>
            </tr>
          </thead>
          <tbody className="[&_tr:last-child]:border-0">
            {patients.map((patient) => (
              <tr
                key={patient.id}
                className="border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer"
                onClick={() => handlePatientClick(patient.id)}
              >
                <td className="py-4 px-6 p-2 align-middle">
                  <div className="flex items-center space-x-3">
                    <Image
                      src={Avatar}
                      alt={`Foto de ${patient.name}`}
                      className="h-10 w-10 rounded-lg object-cover"
                    />
                    <span className="font-medium text-gray-900 hover:text-blue-600 transition-colors">
                      {patient.name} {patient.last_name}
                    </span>
                  </div>
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {patient.age ? `${patient.age} años` : 'N/A'}
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {patient.main_diagnostic_cie || 'Sin diagnóstico'}
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {patient.email}
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {patient.phone}
                </td>
                <td
                  className="py-4 px-6 p-2 align-middle text-center"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="flex justify-center items-center">
                    <PatientActions
                      patient={patient}
                      onViewDetails={onViewDetails}
                      onViewClinicalHistory={onViewClinicalHistory}
                      onEditPatient={onEditPatient}
                      onDeletePatient={onDeletePatient}
                    />
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
