'use client';
import OrderIcon from '../../../components/icons/OrderIcon';
import CalendarIcon from '../../../components/icons/CalendarIcon';
import ConversationIcon from '../../../components/icons/ConversationIcon';
import OtherIcon from '../../../components/icons/OtherIcon';
import useOnboardingStore from '../context/onboardingStore';

const Step4 = ({
  onValidation
}: {
  onValidation: (isValid: boolean) => void;
}) => {
  const { stepData, setStepData } = useOnboardingStore();
  const selectedOptions = stepData.selectedOptions || [];
  const userName = stepData.name || '';

  const options = [
    {
      label: 'Llevar el control de los expedientes de mis pacientes.',
      icon: <OrderIcon />
    },
    {
      label: 'Organizar mi agenda y recordatorios.',
      icon: <CalendarIcon />
    },
    {
      label: 'Mejorar la comunicación con mis pacientes.',
      icon: <ConversationIcon />
    },
    {
      label: 'Otros.',
      icon: <OtherIcon />
    }
  ];

  const handleCheckboxToggle = (option: string) => {
    const newSelectedOptions = selectedOptions.includes(option)
      ? selectedOptions.filter(
          (selectedOption: string) => selectedOption !== option
        )
      : [...selectedOptions, option];

    setStepData('selectedOptions', newSelectedOptions);
    onValidation(newSelectedOptions.length > 0);
  };

  return (
    <>
      <div className="mx-auto flex  w-full max-w-lg flex-col space-y-2 p-2">
        <h1 className="text-center text-2xl font-bold text-[#487FFA]">
          {userName
            ? `${userName}, ¿qué buscas lograr con Segimed?`
            : '¿Qué buscas lograr con Segimed?'}
        </h1>

        <p className="text-start text-base text-[#5F5F5F] sm:text-sm">
          Selecciona todas las que apliquen.
        </p>

        <div className="w-full space-y-4">
          {options.map((option, index) => (
            <label
              key={index}
              className={`flex w-full items-center justify-between border p-1 transition-all duration-200 ${
                selectedOptions.includes(option.label)
                  ? 'border-[#487FFA] bg-[#E8EFFF]'
                  : 'border-gray-300 bg-[#FBFBFB]'
              }`}
            >
              <div className="flex items-center gap-2">
                {option.icon}
                <p className="text-xs text-gray-700 sm:text-xs">
                  {option.label}
                </p>
              </div>
              <input
                type="checkbox"
                checked={selectedOptions.includes(option.label)}
                onChange={() => handleCheckboxToggle(option.label)}
                className="h-3 w-3 rounded-full border-2 border-gray-300 checked:bg-[#487FFA] focus:ring-2 focus:ring-[#487FFA]"
              />
            </label>
          ))}
        </div>
      </div>
    </>
  );
};

export default Step4;
