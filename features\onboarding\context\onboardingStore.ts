'use client';

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { toast } from 'sonner';
import {
  submitOnboarding,
  OnboardingData,
  OnboardingResponse
} from '../actions/onboardingAction';

type OnboardingStore = {
  stepData: Record<string, any>;
  selectedType: 'Particulares' | 'Organizacion' | null;
  setStepData: (key: string, value: any) => void;
  switchUserType: (type: 'Particulares' | 'Organizacion') => void;
  sendDataToBackend: () => Promise<OnboardingResponse>;
};

const useOnboardingStore = create(
  persist<OnboardingStore>(
    (set, get) => ({
      stepData: {},
      selectedType: null,

      setStepData: (key, value) => {
        const currentValue = get().stepData[key];
        // Solo actualizar si el valor es diferente al actual
        if (currentValue !== value) {
          set((state) => ({
            stepData: { ...state.stepData, [key]: value }
          }));
        }
      },

      switchUserType: (newType) => {
        set((state) => {
          let updatedData = { ...state.stepData };

          if (newType === 'Particulares') {
            delete updatedData['Organizacion'];
          } else {
            delete updatedData['Particulares'];
          }

          updatedData['selectedType'] = newType;

          return { stepData: updatedData, selectedType: newType };
        });
      },

      sendDataToBackend: async () => {
        const { stepData } = get();

        try {
          // Intentar obtener el user_id de las cookies si no está en stepData
          let userId = stepData.userId;

          if (!userId) {
            // En el cliente, obtenemos las cookies
            const cookies = document.cookie.split(';');
            const userIdCookie = cookies.find((cookie) =>
              cookie.trim().startsWith('user_id=')
            );

            if (userIdCookie) {
              userId = userIdCookie.split('=')[1].trim();
              console.log('User ID obtenido de cookies:', userId);
            } else {
              console.error('No se encontró user_id en las cookies');
              throw new Error('No se encontró el ID de usuario');
            }
          }

          // Verificar que el OTP haya sido verificado
          if (!stepData.otpVerified) {
            throw new Error('Debe verificar su número de teléfono primero');
          }

          // Obtener la especialidad como número
          let specialties: number[] = [];
          if (stepData.especialidad) {
            // Convertir la especialidad a un ID numérico (simulado)
            // En un caso real, esto debería ser un mapeo a IDs reales de especialidades
            const specialtyIndex = stepData.especialidad ? 1 : 0;
            specialties = [specialtyIndex];
          }

          // Obtener el motivo de registro
          let reason = '';
          if (stepData.selectedOptions && stepData.selectedOptions.length > 0) {
            // Limitar el motivo a 50 caracteres
            reason = stepData.selectedOptions.join(', ').slice(0, 50);
          }

          // Determinar el tipo de usuario
          const userType =
            stepData.selectedType === 'Particulares'
              ? 'individual'
              : 'organization';

          // Obtener el número de empleados/pacientes
          let numberOfEmployees: number | undefined;
          let numberOfPatients: number | undefined;

          if (userType === 'organization') {
            // Convertir el rango de pacientes a un número
            if (stepData.pacientes) {
              const pacientesRange = stepData.pacientes;
              // Extraer el primer número del rango (ej: de "10-20" tomar 10)
              const match = pacientesRange.match(/\d+/);
              if (match) {
                numberOfPatients = parseInt(match[0], 10);
              }
            }

            // Por defecto, si no hay empleados, asumimos 1
            numberOfEmployees = 1;
          } else {
            // Para usuarios individuales, establecer valores por defecto
            numberOfEmployees = 1;
            numberOfPatients = 50; // Valor por defecto para usuarios individuales
          }

          // Map frontend data to backend DTO format
          const onboardingData: OnboardingData = {
            user_id: userId,
            name:
              stepData.name ||
              stepData.organizacion ||
              stepData.organizacionParticular ||
              '',
            type: userType,
            number_of_employees: numberOfEmployees,
            number_of_patients: numberOfPatients,
            reason_register:
              reason || 'Mejorar la gestión de mi consultorio'.slice(0, 50),
            speciality: specialties
          };

          console.log('Enviando datos de onboarding:', onboardingData);

          // Enviar datos al backend
          const response = await submitOnboarding(onboardingData);
          toast.success(response.message || 'Onboarding completado con éxito');
          return response;
        } catch (error) {
          console.error('Error en onboarding:', error);

          if (error instanceof Error) {
            toast.error(`Error: ${error.message}`);
          } else {
            toast.error('Hubo un error al completar el onboarding');
          }

          throw error;
        }
      }
    }),
    {
      name: 'onboardingData', // Nombre del key en localStorage
      storage: createJSONStorage(() => localStorage) // Define localStorage como almacenamiento
    }
  )
);

export default useOnboardingStore;
