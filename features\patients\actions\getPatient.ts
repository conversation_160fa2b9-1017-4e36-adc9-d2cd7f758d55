'use server';

import { get } from '../../../lib/api';

export interface Patient {
  id: string;
  name: string;
  last_name: string;
  image: string;
  age: number;
  main_diagnostic_cie: string;
  email: string;
  phone: string;
  prefix: string;
  identification_type: string;
  identification_number: string;
  health_care_number: string;
}

interface ErrorResponse {
  message: string;
  error: string;
  statusCode: number;
}

export interface PaginatedResponse {
  data: Patient[];
  meta: {
    total: number;
    page: number;
    limit: number;
  };
}

export async function getPatients(
  page = 1,
  pageSize = 10,
  search?: string
): Promise<Patient[]> {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('pageSize', pageSize.toString());

    if (search) {
      queryParams.append('search', search);
    }

    const result = await get<Patient[]>(`/patient?${queryParams.toString()}`);

    return result.map((patient) => ({
      id: patient.id,
      name: patient.name,
      last_name: patient.last_name,
      image: patient.image,
      age: patient.age,
      main_diagnostic_cie: patient.main_diagnostic_cie,
      email: patient.email,
      phone: patient.phone,
      prefix: patient.prefix,
      identification_type: patient.identification_type,
      identification_number: patient.identification_number,
      health_care_number: patient.health_care_number
    }));
  } catch (error) {
    console.error('Error fetching patients:', error);
    return [];
  }
}
