import React from 'react';
import Image from 'next/image';
import { Message } from '@/types/chatbot-alarmas';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';

interface MessageListProps {
  messages: Message[];
  loading: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement>;
}

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  loading,
  messagesEndRef
}) => {
  return (
    <div className="flex flex-col space-y-4 overflow-y-auto p-4">
      {messages.map((message, index) => (
        <div
          key={index}
          className={cn(
            'flex items-start gap-3',
            message.sender === 'bot' ? 'flex-row' : 'flex-row-reverse'
          )}
        >
          <Avatar className="h-8 w-8">
            {message.sender === 'bot' ? (
              <AvatarImage src="/images/segi-avatar.png" alt="Segi" />
            ) : (
              <AvatarImage src="/images/user-avatar.png" alt="Usuario" />
            )}
            <AvatarFallback>
              {message.sender === 'bot' ? 'SG' : 'U'}
            </AvatarFallback>
          </Avatar>

          <div
            className={cn(
              'flex max-w-[80%] flex-col md:max-w-[70%]',
              message.sender === 'bot' ? 'items-start' : 'items-end'
            )}
          >
            <div className="mb-1 text-sm text-muted-foreground">
              {message.sender === 'bot' ? 'Segi' : 'Tú'}
            </div>
            <div
              className={cn(
                'rounded-lg px-4 py-2',
                message.sender === 'bot'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted'
              )}
            >
              {message.message}
            </div>
          </div>
        </div>
      ))}

      {loading && (
        <div className="flex items-start gap-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src="/images/segi-avatar.png" alt="Segi" />
            <AvatarFallback>SG</AvatarFallback>
          </Avatar>
          <div className="flex max-w-[80%] flex-col md:max-w-[70%]">
            <div className="mb-1 text-sm text-muted-foreground">Segi</div>
            <div className="animate-pulse rounded-lg bg-primary/10 px-4 py-2">
              Escribiendo...
            </div>
          </div>
        </div>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
};
