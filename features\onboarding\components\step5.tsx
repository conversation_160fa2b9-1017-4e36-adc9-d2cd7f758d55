'use client';
import CheckedIcon from '../../../components/icons/CheckedIcon';
import CrownIcon from '../../../components/icons/CrownIcon';
import useOnboardingStore from '../context/onboardingStore';

const Step5 = ({
  onValidation
}: {
  onValidation: (isValid: boolean) => void;
}) => {
  const { stepData, setStepData } = useOnboardingStore();
  const userName = stepData.name || '';

  const handleSelection = (type: 'premium' | 'free') => {
    setStepData('selectedPlan', type);
    onValidation(true);
  };

  return (
    <>
      <div className="mx-auto flex w-full max-w-xl flex-col space-y-2 p-2">
        <h1 className="text-center text-2xl font-bold text-[#487FFA]">
          {userName
            ? `${userName}, ahorra un 95% en tus primeros 3 meses.`
            : 'Ahorra un 95% en tus primeros 3 meses.'}
        </h1>

        <p className="text-start text-base text-[#5F5F5F] sm:text-sm">
          Suscribete ahora mismo y ahorra un 95% en tus primeros 3 meses de
          Segimed Premium
        </p>

        <div className="flex w-full flex-col gap-1">
          <label
            className={`flex cursor-pointer items-center justify-between p-2 transition-all duration-200 ${
              stepData.selectedPlan === 'premium'
                ? 'border-[#487FFA] bg-[#E8EFFF] ring-1 ring-[#487FFA]'
                : 'border border-gray-300 bg-gray-100 hover:bg-[#d7e5fd]'
            }`}
          >
            <div className="flex flex-col">
              <div>
                <h2 className="flex items-center text-lg font-semibold text-[#487FFA]">
                  Segimed Premium <CrownIcon />
                </h2>
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-lg text-gray-500 line-through">
                    $153
                  </span>
                  <span className="text-xl font-bold text-[#487FFA]">
                    $9.25
                  </span>
                </div>
              </div>

              <ul className="mt-2 list-none pl-6 text-sm text-gray-600">
                <li className="flex items-center gap-1 text-xs">
                  <CheckedIcon />
                  Agenda automatizada con recordatorio a WhatsApp.
                </li>
                <li className="flex items-center gap-1 text-xs">
                  <CheckedIcon />
                  Historia clínica 100% personalizada por especialidad.
                </li>
                <li className="flex items-center gap-1 text-xs">
                  <CheckedIcon />
                  Teleconsulta habilitado.
                </li>
                <li className="flex items-center gap-1 text-xs">
                  <CheckedIcon />
                  Chat para hablar con tus pacientes.
                </li>
                <li className="flex items-center gap-1 text-xs">
                  <CheckedIcon />
                  Control de consultas y cargos a tus pacientes.
                </li>
                <li className="flex items-center gap-1 text-xs">
                  <CheckedIcon />
                  Pagos Online.
                </li>
              </ul>
            </div>
            <input
              type="radio"
              name="plan"
              value="premium"
              checked={stepData.selectedPlan === 'premium'}
              onChange={() => handleSelection('premium')}
              className="h-4 w-4 rounded-full border-[#487FFA] bg-white"
            />
          </label>

          <label
            className={`flex cursor-pointer items-center justify-between p-2 transition-all duration-200 ${
              stepData.selectedPlan === 'free'
                ? 'border-[#487FFA] bg-[#E8EFFF] ring-1 ring-[#487FFA]'
                : 'border border-gray-300 bg-gray-100 hover:bg-[#d7e5fd]'
            }`}
          >
            <h2 className="text-sm font-medium text-gray-700">
              Continuar con la prueba gratuita de 14 días.
            </h2>
            <input
              type="radio"
              name="plan"
              value="free"
              checked={stepData.selectedPlan === 'free'}
              onChange={() => handleSelection('free')}
              className="h-4 w-4 rounded-full border-[#487FFA] bg-white"
            />
          </label>
        </div>
      </div>
    </>
  );
};

export default Step5;
