'use client';

import React, { useState } from 'react';
import { Trash } from 'lucide-react';
import useConsultationStore from '../../context/consultationStore';

const LaboratoryRequest = () => {
  const { requests, addRequests, removeRequest } = useConsultationStore();
  const [requestInput, setRequestInput] = useState('');
  const [loading, setLoading] = useState(false);

  const sendRequestToBackend = async (request: string) => {
    try {
      setLoading(true);
      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ request })
      });

      if (!response.ok) {
        throw new Error('Error al guardar el procedimiento');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error al enviar los datos:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRequestBlur = async () => {
    if (requestInput.trim() !== '') {
      addRequests(requestInput);
      await sendRequestToBackend(requestInput);
      setRequestInput('');
    }
  };

  return (
    <div className="mx-auto w-full max-w-2xl rounded-lg border border-gray-300 bg-white  p-4">
      <h1 className="mb-2 text-sm font-semibold text-gray-800">
        Solicitudes de laboratorio e imágenes
      </h1>
      <div className="mb-4 flex gap-2">
        <input
          type="text"
          placeholder="Indicar Solicitud..."
          className="w-full rounded border border-[#DCDBDB] bg-white p-2"
          value={requestInput}
          onChange={(e) => setRequestInput(e.target.value)}
          onBlur={handleRequestBlur}
        />
        <button
          className="rounded bg-blue-500 px-2 py-2 text-white"
          onClick={handleRequestBlur}
        >
          {loading ? 'Guardando...' : 'Agregar'}
        </button>
      </div>
      <div className="grid grid-cols-2 gap-4  rounded">
        {Object.entries(requests).map(([request]) => (
          <div
            key={request}
            className="flex w-full flex-col justify-between rounded border border-[#DCDBDB] bg-gray-100 p-3"
          >
            <div className="mb-2 flex items-center justify-between ">
              <p className="font-lg">{request}</p>
              <button
                className="text-red-500"
                onClick={() => removeRequest(request)}
              >
                <Trash size={20} />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LaboratoryRequest;
