'use client';

import { AlarmasChatUIProps } from '../../../types/chatbot-alarmas';
import { Button } from '../../../components/ui/button';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

export const AlarmasChatUI = ({ alarmas }: AlarmasChatUIProps) => {
  return (
    <div>
      {/* HEADER */}
      <div className="flex w-full justify-center py-2">
        <h1 className="text-xl font-semibold">Listado de Alarmas</h1>
      </div>
      {/* FIN DEL HEADER */}
      {/* TABLA */}
      <Table className="w-full border-collapse border border-gray-300 px-10">
        <TableHeader>
          <TableRow className="grid grid-cols-[20%,15%,20%,15%,15%,10%] items-center border-b border-gray-300 bg-gray-100 px-10 text-lg">
            <TableHead className="text-left">Paciente</TableHead>
            <TableHead className="text-left">Status</TableHead>
            <TableHead className="text-left">Descripción</TableHead>
            <TableHead className="text-left">Fecha</TableHead>
            <TableHead className="text-left">Hora</TableHead>
            <TableHead className="text-left">Acciones</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {/* {consultations.map((consultation, index) => (
            <TableRow
              key={index}
              className="grid grid-cols-[20%,15%,15%,20%,20%,10%] items-center border-b border-gray-200 px-10"
            >
              <TableCell className="flex items-center gap-[5px]">
                <Image
                  src={Avatar}
                  alt="Consultation Avatar"
                  className="h-10 w-10 rounded-[5px]"
                />
                {consultation.name}
              </TableCell>
              <TableCell>{consultation.consultationDate}</TableCell>
              <TableCell>{consultation.reason}</TableCell>
              <TableCell>{consultation.type}</TableCell>
              <TableCell>{consultation.mount}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="hover:text-blue-600">
                      <Ellipsis />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    side="bottom"
                    align="start"
                    className="w-48"
                  >
                    <DropdownMenuItem
                      onSelect={() => handleViewConsultation(consultation.id)}
                    >
                      Ver Consulta
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onSelect={() => console.log('Ver Perfil')}
                    >
                      Ver Perfil
                    </DropdownMenuItem>
                    <DropdownMenuItem onSelect={() => console.log('Editar')}>
                      Editar
                    </DropdownMenuItem>
                    <DropdownMenuItem onSelect={() => console.log('Eliminar')}>
                      Eliminar
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))} */}
        </TableBody>
      </Table>
      {/* FIN DE TABLA */}
    </div>
  );
};
