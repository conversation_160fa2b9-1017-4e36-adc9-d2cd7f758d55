'use server';

import { patch } from '../../../lib/api'; // Assuming patch is exported from lib/api.ts
import { revalidatePath } from 'next/cache';

// Define the expected response type (adjust if needed based on actual API response)
interface CancelResponse {
  success: boolean;
  message?: string;
  // Include other fields if the API returns more details
}

// Define the data structure for the PATCH request body
interface CancelPayload {
  status: 'cancelada';
  reason: string;
  // comments?: string; // Add if comments are needed for cancellation
}

/**
 * Cancels an appointment by updating its status.
 * @param appointmentId The ID of the appointment to cancel.
 * @param reason The reason for cancellation.
 * @returns The result of the cancellation operation.
 */
export async function cancelAppointment(
  appointmentId: string,
  reason: string
): Promise<CancelResponse> {
  if (!appointmentId) {
    throw new Error('Appointment ID is required to cancel.');
  }
  if (!reason) {
    // Consider if an empty reason is allowed or if it should be mandatory
    throw new Error('Cancellation reason is required.');
  }

  const payload: CancelPayload = {
    status: 'cancelada',
    reason: reason
  };

  try {
    // Construct the endpoint URL
    const endpoint = `/appointments/${appointmentId}/status`;

    // Call the patch function from lib/api
    const response = await patch<CancelResponse>(endpoint, payload);

    // Revalidate the path where appointments are displayed, e.g., the dashboard or agenda page
    // Adjust the path as necessary for your application structure
    revalidatePath('/dashboard'); // Or specific agenda path

    return response;
  } catch (error) {
    console.error('Error cancelling appointment:', error);

    // Handle specific error types or messages if possible
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(
        `Failed to cancel appointment: ${error.message as string}`
      );
    }

    throw new Error('Failed to cancel appointment due to an unexpected error.');
  }
}
