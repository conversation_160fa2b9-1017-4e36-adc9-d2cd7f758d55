'use client';

import React, { useState } from 'react';
import {
  Ellipsis,
  ArrowUpDown,
  Weight,
  PersonStanding,
  Thermometer,
  Activity,
  AudioWaveform,
  Heart
} from 'lucide-react';

const VitalSings = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [vitalSigns, setVitalSings] = useState({
    altura: '1.80m',
    peso: '86.00 kg',
    imc: '24.69 kg/m2',
    temperatura: '37.50 C',
    frecuenciaRespiratoria: '17.00/rm',
    sistolica: '120 mmHg',
    diastolica: '80 mmHg',
    frecuenciaCardiaca: '80.00 bpm'
  });

  const handleEditToggle = () => {
    setIsEditing(true);
    setIsMenuOpen(false);
  };

  const handleSave = () => {
    setIsEditing(false);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    key: string
  ) => {
    setVitalSings((prev) => ({
      ...prev,
      [key]: e.target.value
    }));
  };

  return (
    <div className=" w-[370px] rounded-sm border border-[#DCDBDB] bg-[#FBFBFB] p-4">
      <div className="flex flex-row justify-between">
        <h1 className="mb-2 text-sm font-semibold text-gray-800">
          Signos Vitales
        </h1>
        <div className="relative">
          <Ellipsis
            className="text-bluePrimary"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          />
          {isMenuOpen && (
            <div className="absolute right-0 mt-2 w-40 rounded-md border bg-white shadow-lg">
              <button
                onClick={handleEditToggle}
                className="w-full px-4 py-2 text-left text-xs hover:bg-gray-100"
              >
                Editar signos vitales
              </button>
            </div>
          )}
        </div>
      </div>

      {Object.entries(vitalSigns).map(([key, value]) => {
        const labels: Record<string, string> = {
          altura: 'Altura',
          peso: 'Peso',
          imc: 'IMC',
          temperatura: 'Temperatura',
          frecuenciaRespiratoria: 'Frec. Respiratoria',
          sistolica: 'Sistólica',
          diastolica: 'Diastólica',
          frecuenciaCardiaca: 'Frecuencia Cardíaca'
        };

        const icons: Record<string, JSX.Element> = {
          altura: <ArrowUpDown className="m-1 text-bluePrimary" />,
          peso: <Weight className="m-1 text-bluePrimary" />,
          imc: <PersonStanding className="m-1 text-bluePrimary" />,
          temperatura: <Thermometer className="m-1 text-bluePrimary" />,
          frecuenciaRespiratoria: <Activity className="m-1 text-bluePrimary" />,
          sistolica: <AudioWaveform className="m-1 text-bluePrimary" />,
          diastolica: <AudioWaveform className="m-1 text-bluePrimary" />,
          frecuenciaCardiaca: <Heart className="m-1 text-bluePrimary" />
        };

        return (
          <div key={key} className="m-2 flex flex-row justify-between">
            <div className="flex flex-row">
              {icons[key]}
              <p>{labels[key]}</p>
            </div>
            <div>
              {isEditing ? (
                <input
                  type="text"
                  value={value}
                  onChange={(e) => handleChange(e, key)}
                  className="w-20 border bg-white p-1 text-right text-xs"
                />
              ) : (
                <p>{value}</p>
              )}
            </div>
          </div>
        );
      })}

      {isEditing && (
        <button
          onClick={handleSave}
          className="mt-4 w-full rounded-md bg-blue-500 p-2 text-white hover:bg-blue-600"
        >
          Guardar
        </button>
      )}
    </div>
  );
};

export default VitalSings;
