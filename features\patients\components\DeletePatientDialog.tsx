'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog';
import { DashboardPatient } from '../types/dashboardTypes';

interface DeletePatientDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  patient: DashboardPatient | null;
  onCancel: () => void;
  onConfirm: () => void;
}

export function DeletePatientDialog({
  isOpen,
  onOpenChange,
  patient,
  onCancel,
  onConfirm
}: DeletePatientDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
          <AlertDialogDescription>
            Esta acción eliminará la asociación del paciente{' '}
            <strong>{patient?.name}</strong> con tu cuenta. Esta acción no se
            puede deshacer directamente aquí, pero el paciente podría ser
            re-asociado si es necesario.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onCancel}>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className="bg-red-600 hover:bg-red-700"
          >
            Confirmar Eliminación
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
