'use client';
import { useState } from 'react';
import useOnboardingStore from '../context/onboardingStore';

interface Step3aProps {
  onValidation: (isValid: boolean) => void;
}

const Step3a = ({ onValidation }: Step3aProps) => {
  const especialidadMedica = [
    'Seleccione una especialidad',
    'Alergología',
    'Anestesiología y reanimación',
    'Aparato Digestivo',
    'Cardiología',
    'Dermatología',
    'Endocrinología y nutrición',
    'Geriatría',
    'Hematología y hemoterapia',
    'Medicina de la educación física y el deporte',
    'Medicina espacial',
    'Medicina intensiva',
    'Medicina interna',
    'Medicina legal y forense',
    'Medicina preventiva y salud pública',
    'Medicina del trabajo',
    'Nefrología',
    'Neumología',
    'Neurología',
    'Neurofisiología clínica',
    'Oncología médica',
    'Oncología radioterápica',
    '<PERSON>ediatr<PERSON>',
    'Rehabilitación',
    'Reumatología',
    'Medicina familiar y comunitaria',
    'Biomedicina'
  ];

  const [error, setError] = useState('');

  const { stepData, setStepData } = useOnboardingStore();
  const userName = stepData.name || '';

  const handleValidation = () => {
    const isValidName = /^[a-zA-Z\s]+$/.test(
      stepData.organizacionParticular || ''
    );
    if (!stepData.especialidad || !stepData.organizacionParticular) {
      setError('Por favor, complete todos los campos');
      onValidation(false);
    } else if (!isValidName || stepData.organizacionParticular.length < 3) {
      setError('El nombre de la organización debe tener al menos 3 caracteres');
      onValidation(false);
    } else {
      setError('');
      onValidation(true);
    }
  };

  return (
    <div className="flex w-full flex-col items-center space-y-6">
      <h1 className="text-center text-2xl font-bold text-[#487FFA]">
        {userName ? `${userName}, contanos sobre vos` : 'Contanos sobre vos'}
      </h1>

      <p className="text-start text-base text-[#5F5F5F] sm:text-sm">
        Configuremos tu perfil, puedes cambiarlo más tarde.
      </p>

      <div className="flex w-full flex-col space-y-6">
        <div className="flex flex-col space-y-2">
          <label htmlFor="especialidad" className="text-sm text-[#5F5F5F]">
            Especialidad
          </label>
          <select
            name="especialidad"
            id="especialidad"
            value={stepData.especialidad || ''}
            onChange={(e) => {
              setStepData('especialidad', e.target.value);
              handleValidation();
            }}
            className="w-full  border border-gray-300 bg-white p-2 text-sm focus:border-[#487FFA] focus:outline-none focus:ring focus:ring-[#487FFA] sm:text-base"
          >
            {especialidadMedica.map((especialidad, index) => (
              <option
                key={index}
                value={
                  especialidad === 'Seleccione una especialidad'
                    ? ''
                    : especialidad
                }
              >
                {especialidad}
              </option>
            ))}
          </select>
        </div>

        <div className="flex flex-col space-y-2">
          <label htmlFor="organización" className="text-sm text-[#5F5F5F]">
            Nombre de la organización
          </label>
          <input
            id="organizacion"
            value={stepData.organizacionParticular || ''}
            onChange={(e) => {
              setStepData('organizacionParticular', e.target.value);
              handleValidation();
            }}
            className="w-full  border border-gray-300 bg-white p-2 text-sm focus:border-[#487FFA] focus:outline-none focus:ring focus:ring-[#487FFA]"
            type="text"
            placeholder="Ingrese el nombre de la organización"
          />
        </div>

        {error && <p className="text-sm text-red-500">{error}</p>}
      </div>
    </div>
  );
};

export default Step3a;
