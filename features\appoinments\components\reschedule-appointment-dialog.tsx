'use client';

import { useState, useEffect, useTransition } from 'react'; // Import hooks
import { es } from 'date-fns/locale';
import { format, addMinutes, parse } from 'date-fns'; // Import date-fns functions
import { toast } from 'sonner'; // Import toast
import { getAvailableSlots } from '../actions/getAvailableSlots'; // Import actions
import { cancelAppointment } from '../actions/cancelAppointment';
import { createAppointment } from '../actions/createAppointment';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface RescheduleAppointmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  // Removed onReschedule prop
  appointmentId: string; // ID of the appointment being rescheduled
  physicianId: string; // ID of the physician (for fetching slots and creating new appt)
  patientId: string; // ID of the patient
  consultationReason: string; // Original reason
  originalComments?: string; // Original comments (optional)
  currentDate: Date; // Initial date shown in calendar
}

// Removed mock timeSlots

export function RescheduleAppointmentDialog({
  isOpen,
  onClose,
  appointmentId,
  physicianId,
  patientId,
  consultationReason,
  originalComments,
  currentDate
}: RescheduleAppointmentDialogProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined); // Start with no date selected for rescheduling
  const [selectedTime, setSelectedTime] = useState<string | undefined>(
    undefined
  ); // Store only start time "HH:mm"

  // State for available slots
  const [availableSlots, setAvailableSlots] = useState<string[]>([]);
  const [isLoadingSlots, startSlotsTransition] = useTransition();
  const [slotsError, setSlotsError] = useState<string | null>(null);

  // State for the rescheduling process
  const [isRescheduling, startRescheduleTransition] = useTransition();
  // Effect to fetch slots
  useEffect(() => {
    if (physicianId && selectedDate) {
      const formattedDate = format(selectedDate, 'yyyy-MM-dd');
      setAvailableSlots([]); // Clear previous slots
      setSlotsError(null); // Clear previous errors
      setSelectedTime(undefined); // Reset selected time

      startSlotsTransition(async () => {
        try {
          const response = await getAvailableSlots(physicianId, formattedDate);
          if (response && response.slots) {
            setAvailableSlots(response.slots);
            if (response.slots.length === 0) {
              setSlotsError('No hay horarios disponibles para esta fecha.');
            }
          } else {
            setAvailableSlots([]);
            setSlotsError('No se pudieron obtener los horarios.');
          }
        } catch (error) {
          console.error('Failed to fetch slots:', error);
          const errorMessage =
            error instanceof Error
              ? error.message
              : 'Error al cargar horarios.';
          setSlotsError(errorMessage);
          setAvailableSlots([]);
        }
      });
    } else {
      setAvailableSlots([]); // Clear slots if no physician or date
      setSlotsError(null);
    }
  }, [physicianId, selectedDate, startSlotsTransition]);

  // Handle Reschedule Logic
  const handleReschedule = () => {
    if (
      !selectedDate ||
      !selectedTime ||
      !appointmentId ||
      !patientId ||
      !physicianId
    ) {
      toast.error('Faltan datos para reprogramar la cita.');
      return;
    }

    startRescheduleTransition(async () => {
      try {
        // 1. Cancel the original appointment
        // Using a generic reason, adjust if needed
        await cancelAppointment(
          appointmentId,
          'Cita reprogramada por el usuario'
        );
        toast.info('Cita original cancelada.');

        // 2. Create the new appointment
        // Combine date and time, calculate end time (assuming 30 min duration)
        const startTimeObj = parse(selectedTime, 'HH:mm', selectedDate);
        const endTimeObj = addMinutes(startTimeObj, 30); // ASSUMPTION: 30 min duration

        const newAppointmentData = {
          patient_id: patientId,
          physician_id: physicianId,
          consultation_reason: consultationReason,
          comments: originalComments, // Pass original comments or allow new ones
          start: startTimeObj.toISOString(), // Format as ISO string
          end: endTimeObj.toISOString() // Format as ISO string
        };

        await createAppointment(newAppointmentData);

        toast.success('Cita reprogramada exitosamente!');
        onClose(); // Close dialog on success
      } catch (error) {
        console.error('Error rescheduling appointment:', error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Ocurrió un error inesperado.';
        // Provide more specific feedback if possible (e.g., cancellation failed vs. creation failed)
        toast.error(`Error al reprogramar: ${errorMessage}`);
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="p-0 sm:max-w-[800px]">
        <DialogHeader className="border-b p-6">
          {/* Simplified Header */}
          <DialogTitle className="text-xl font-semibold">
            Reprogramar Turno
          </DialogTitle>
          {/* Removed doctor select and view toggles */}
        </DialogHeader>

        <div className="flex gap-6 p-6">
          {/* Calendar */}
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={(newDate) => {
              setSelectedDate(newDate);
              setSelectedTime(undefined); // Reset time when date changes
              setAvailableSlots([]); // Clear old slots
              setSlotsError(null); // Clear old errors
            }}
            disabled={(date) =>
              date < new Date(new Date().setHours(0, 0, 0, 0))
            } // Disable past dates
            className="rounded-md border"
            locale={es}
            // Consider adding month navigation if needed
          />

          {/* Available Slots */}
          <div className="flex-1">
            <h3 className="mb-4 font-medium">
              Horarios disponibles para{' '}
              {selectedDate
                ? format(selectedDate, 'PPP', { locale: es })
                : 'Seleccione una fecha'}
            </h3>
            <div className="grid grid-cols-3 gap-2">
              {isLoadingSlots ? (
                <p className="col-span-3 text-sm text-muted-foreground">
                  Cargando horarios...
                </p>
              ) : slotsError ? (
                <p className="col-span-3 text-sm text-red-500">{slotsError}</p>
              ) : availableSlots.length > 0 ? (
                availableSlots.map((time) => (
                  <Button
                    key={time}
                    variant="outline"
                    size="sm"
                    className={cn(
                      'justify-center',
                      selectedTime === time &&
                        'border-primary ring-1 ring-primary' // Highlight selected time
                    )}
                    onClick={() => setSelectedTime(time)}
                  >
                    {time}
                  </Button>
                ))
              ) : (
                <p className="col-span-3 text-sm text-muted-foreground">
                  {selectedDate
                    ? 'No hay horarios disponibles.'
                    : 'Seleccione una fecha para ver los horarios.'}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 border-t bg-gray-50 p-6">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button
            onClick={handleReschedule}
            disabled={
              !selectedDate || !selectedTime || isRescheduling || isLoadingSlots
            }
          >
            {isRescheduling ? 'Reprogramando...' : 'Confirmar Reprogramación'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
