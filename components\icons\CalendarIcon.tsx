import React from 'react';

const CalendarIcon = () => {
  return (
    <div>
      <svg
        width="24.000000"
        height="24.000000"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <clipPath id="clip4495_5599">
            <rect
              id="svg"
              width="24.000000"
              height="24.000000"
              fill="white"
              fillOpacity="0"
            />
          </clipPath>
        </defs>
        <g clipPath="url(#clip4495_5599)">
          <path
            id="path"
            d="M0 0L24 0L24 24L0 24L0 0Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M24 0L24 24L0 24L0 0L24 0Z"
            stroke="#000000"
            strokeOpacity="0"
            strokeWidth="2.000000"
            strokeLinejoin="round"
          />
          <path
            id="path"
            d="M4 7C4 6.44 4.19 5.97 4.58 5.58C4.97 5.19 5.44 5 6 5L18 5C18.55 5 19.02 5.19 19.41 5.58C19.8 5.97 20 6.44 20 7L20 19C20 19.55 19.8 20.02 19.41 20.41C19.02 20.8 18.55 21 18 21L6 21C5.44 21 4.97 20.8 4.58 20.41C4.19 20.02 4 19.55 4 19L4 7Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M4.58 5.58C4.97 5.19 5.44 5 6 5L18 5C18.55 5 19.02 5.19 19.41 5.58C19.8 5.97 20 6.44 20 7L20 19C20 19.55 19.8 20.02 19.41 20.41C19.02 20.8 18.55 21 18 21L6 21C5.44 21 4.97 20.8 4.58 20.41C4.19 20.02 4 19.55 4 19L4 7C4 6.44 4.19 5.97 4.58 5.58Z"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
          />
          <path
            id="path"
            d="M15.02 3.02L15 3C15 2.43 15.43 2 16 2C16.56 2 17 2.43 17 3L16.98 3.02L15.02 3.02ZM16.98 6.97L17 7C17 7.56 16.56 8 16 8C15.43 8 15 7.56 15 7L15.02 6.97L16.98 6.97Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M16 3L16 7"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M7.02 3.02L7 3C7 2.43 7.43 2 8 2C8.56 2 9 2.43 9 3L8.97 3.02L7.02 3.02ZM8.97 6.97L9 7C9 7.56 8.56 8 8 8C7.43 8 7 7.56 7 7L7.02 6.97L8.97 6.97Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M8 3L8 7"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M4.02 11.98L4 12C3.43 12 3 11.56 3 11C3 10.43 3.43 10 4 10L4.02 10.02L4.02 11.98ZM19.98 10.02L20 10C20.56 10 21 10.43 21 11C21 11.56 20.56 12 20 12L19.98 11.98L19.98 10.02Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M4 11L20 11"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M11.02 15.98L11 16C10.43 16 10 15.56 10 15C10 14.43 10.43 14 11 14L11.02 14.02L11.02 15.98ZM11.98 14.02L12 14C12.56 14 13 14.43 13 15C13 15.56 12.56 16 12 16L11.98 15.98L11.98 14.02Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M11 15L12 15"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M11.02 15.02L11 15C11 14.43 11.43 14 12 14C12.56 14 13 14.43 13 15L12.98 15.02L11.02 15.02ZM12.98 17.98L13 18C13 18.56 12.56 19 12 19C11.43 19 11 18.56 11 18L11.02 17.98L12.98 17.98Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M12 15L12 18"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
        </g>
      </svg>
    </div>
  );
};

export default CalendarIcon;
