import { io } from 'socket.io-client';
import Cookies from 'js-cookie';

const getAuthToken = () => {
  // Obtener el token de la cookie auth_token
  const token = Cookies.get('auth_token');
  console.log(token);
  if (!token) {
    console.warn('No se encontró token de autenticación');
    return null;
  }
  return token;
};

// Initialize socket connection
export const socket = io(
  process.env.NEXT_PUBLIC_SOCKET_URL || 'ws://localhost:3000/alarmas',
  {
    autoConnect: false, // Cambiamos a false para controlar la conexión manualmente
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    extraHeaders: {
      Authorization: `Bearer ${getAuthToken()}`
    }
  }
);

// Función para actualizar el token y reconectar si es necesario
export const connectWithAuth = () => {
  const token = getAuthToken();
  if (!token) {
    console.error('No hay token disponible para la conexión WebSocket');
    return false;
  }

  // Actualizar los headers con el nuevo token
  socket.io.opts.extraHeaders = {
    Authorization: `Bearer ${token}`
  };

  // Si el socket no está conectado, intentar conectar
  if (!socket.connected) {
    socket.connect();
  }

  return true;
};

socket.on('connect_error', (error) => {
  console.error('Error de conexión WebSocket:', error);
  if (
    error.message === 'Invalid token' ||
    error.message === 'Unauthorized connection'
  ) {
    // Si el error es de autenticación, intentar reconectar con un nuevo token
    const reconnected = connectWithAuth();
    if (!reconnected) {
      console.error('No se pudo reconectar: token no disponible');
    }
  }
});
