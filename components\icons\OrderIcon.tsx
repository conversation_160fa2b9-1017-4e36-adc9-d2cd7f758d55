import React from 'react';

const OrderIcon = () => {
  return (
    <div>
      <svg
        width="24.000000"
        height="24.000000"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <clipPath id="clip4495_5529">
            <rect
              id="svg"
              width="24.000000"
              height="24.000000"
              fill="white"
              fillOpacity="0"
            />
          </clipPath>
        </defs>
        <g clipPath="url(#clip4495_5529)">
          <path
            id="path"
            d="M0 0L24 0L24 24L0 24L0 0Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M24 0L24 24L0 24L0 0L24 0Z"
            stroke="#000000"
            strokeOpacity="0"
            strokeWidth="2.000000"
            strokeLinejoin="round"
          />
          <path
            id="path"
            d="M8.97 4.02L9 4C9.56 4 10 4.43 10 5C10 5.56 9.56 6 9 6L8.97 5.97L8.97 4.02ZM15.02 5.97L15 6C14.43 6 14 5.56 14 5C14 4.43 14.43 4 15 4L15.02 4.02L15.02 5.97Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M9 5L7 5C6.44 5 5.97 5.19 5.58 5.58C5.19 5.97 5 6.44 5 7L5 19C5 19.55 5.19 20.02 5.58 20.41C5.97 20.8 6.44 21 7 21L17 21C17.55 21 18.02 20.8 18.41 20.41C18.8 20.02 19 19.55 19 19L19 7C19 6.44 18.8 5.97 18.41 5.58C18.02 5.19 17.55 5 17 5L15 5"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M9 5C9 4.44 9.19 3.97 9.58 3.58C9.97 3.19 10.44 3 11 3L13 3C13.55 3 14.02 3.19 14.41 3.58C14.8 3.97 15 4.44 15 5C15 5.55 14.8 6.02 14.41 6.41C14.02 6.8 13.55 7 13 7L11 7C10.44 7 9.97 6.8 9.58 6.41C9.19 6.02 9 5.55 9 5Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M9.58 3.58C9.97 3.19 10.44 3 11 3L13 3C13.55 3 14.02 3.19 14.41 3.58C14.8 3.97 15 4.44 15 5C15 5.55 14.8 6.02 14.41 6.41C14.02 6.8 13.55 7 13 7L11 7C10.44 7 9.97 6.8 9.58 6.41C9.19 6.02 9 5.55 9 5C9 4.44 9.19 3.97 9.58 3.58Z"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
          />
          <path
            id="path"
            d="M8.02 13.02L8 13C8 12.43 8.43 12 9 12C9.56 12 10 12.43 10 13L9.97 13.02L8.02 13.02ZM9.97 16.98L10 17C10 17.56 9.56 18 9 18C8.43 18 8 17.56 8 17L8.02 16.98L9.97 16.98Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M9 13L9 17"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M11.02 16.02L11 16C11 15.43 11.43 15 12 15C12.56 15 13 15.43 13 16L12.98 16.02L11.02 16.02ZM12.98 16.98L13 17C13 17.56 12.56 18 12 18C11.43 18 11 17.56 11 17L11.02 16.98L12.98 16.98Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M12 16L12 17"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M14.02 15.02L14 15C14 14.43 14.43 14 15 14C15.56 14 16 14.43 16 15L15.98 15.02L14.02 15.02ZM15.98 16.98L16 17C16 17.56 15.56 18 15 18C14.43 18 14 17.56 14 17L14.02 16.98L15.98 16.98Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M15 15L15 17"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
          <path
            id="path"
            d="M11.02 16.02L11 16C11 15.43 11.43 15 12 15C12.56 15 13 15.43 13 16L12.98 16.02L11.02 16.02ZM12.98 16.98L13 17C13 17.56 12.56 18 12 18C11.43 18 11 17.56 11 17L11.02 16.98L12.98 16.98Z"
            fill="#000000"
            fillOpacity="0"
            fillRule="nonzero"
          />
          <path
            id="path"
            d="M12 16L12 17"
            stroke="#487FFA"
            strokeOpacity="1.000000"
            strokeWidth="2.000000"
            strokeLinejoin="round"
            strokeLinecap="round"
          />
        </g>
      </svg>
    </div>
  );
};

export default OrderIcon;
