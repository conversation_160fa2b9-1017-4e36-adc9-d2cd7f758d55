import { z } from 'zod';

/*  ----------------------  Reutilizables      ------------- */

const emailSchema = z
  .string()
  .min(1, { message: 'Debe ingresar un email' })
  .email({ message: 'Debe ingresar un email válido' });

const passwordSchema = z
  .string()
  .min(6, { message: 'La contraseña debe tener al menos 6 caracteres' })
  .regex(/[A-Z]/, { message: 'Debe contener al menos una letra mayúscula' })
  .regex(/[a-z]/, { message: 'Debe contener al menos una letra minúscula' })
  .regex(/\d/, { message: 'Debe contener al menos un número' })
  .regex(/[@#$!%*?&]/, {
    message: 'Debe contener al menos un carácter especial'
  });

const confirmPasswordSchema = z
  .string()
  .min(1, 'Debes confirmar tu contraseña');

/* ----------------------------LOGIN-------------------------*/

export const LoginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, { message: 'La contraseña no puede estar vacía' })
});

export type LoginSchemaType = z.infer<typeof LoginSchema>;

/* ----------------------------FORGOT-------------------------*/

export const ForgotSchema = z.object({
  email: emailSchema
});

export type ForgotSchemaType = z.infer<typeof ForgotSchema>;

/* ----------------------------RECOVERY-------------------------*/

export const RecoverySchema = z
  .object({
    password: passwordSchema,
    confirmPassword: confirmPasswordSchema
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Las contraseñas no coinciden',
    path: ['confirmPassword']
  });

export type RecoverySchemaType = z.infer<typeof RecoverySchema>;
