'use client';

import React from 'react';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { IconSendMensaje } from '../../../components/icons/IconSendMensaje';

interface ChatInputProps {
  messageInput: string;
  setMessageInput: (value: string) => void;
  onSendMessage: () => void;
  onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  loading: boolean;
  disabled?: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  messageInput,
  setMessageInput,
  onSendMessage,
  onKeyDown,
  loading,
  disabled = false
}) => {
  return (
    <div className="flex items-center gap-2 border-t bg-card p-4">
      <Input
        value={messageInput}
        onChange={(e) => setMessageInput(e.target.value)}
        onKeyDown={onKeyDown}
        placeholder={disabled ? 'Chat finalizado' : 'Escribe tu mensaje...'}
        disabled={loading || disabled}
        className="flex-1"
      />
      <Button
        onClick={onSendMessage}
        disabled={loading || disabled || !messageInput.trim()}
        size="icon"
        className="bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50"
      >
        <IconSendMensaje className="h-5 w-5" />
        <span className="sr-only">Enviar mensaje</span>
      </Button>
    </div>
  );
};
