import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Login from '../Login';
import { loginUser } from '../../actions/loginCredentials';

// Mock the loginUser function
jest.mock('../../actions/loginCredentials', () => ({
  loginUser: jest.fn()
}));

describe('Login Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the login form correctly', () => {
    render(<Login />);

    // Check if the title is rendered
    expect(screen.getByText('Iniciar sesión')).toBeInTheDocument();

    // Check if form elements are rendered
    expect(
      screen.getByPlaceholderText('Ingrese su correo electronico')
    ).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText('Ingrese su contraseña')
    ).toBeInTheDocument();
    expect(
      screen.getByText('Iniciar sesión', { selector: 'button' })
    ).toBeInTheDocument();
    expect(screen.getByText('Recuperar Contraseña')).toBeInTheDocument();
    expect(screen.getByText('¿No tienes una cuenta?')).toBeInTheDocument();
  });

  it('shows validation errors for empty fields', async () => {
    render(<Login />);

    // Submit the form without filling the fields
    fireEvent.click(screen.getByText('Iniciar sesión', { selector: 'button' }));

    // Check if validation errors are displayed
    await waitFor(() => {
      expect(
        screen.getByText('El correo electrónico es requerido')
      ).toBeInTheDocument();
      expect(
        screen.getByText('La contraseña es requerida')
      ).toBeInTheDocument();
    });
  });

  it('calls loginUser with correct data on form submission', async () => {
    // Mock successful login
    (loginUser as jest.Mock).mockResolvedValueOnce({
      user: { name: 'Test User', email: '<EMAIL>' }
    });

    render(<Login />);

    // Fill in the form
    fireEvent.change(
      screen.getByPlaceholderText('Ingrese su correo electronico'),
      {
        target: { value: '<EMAIL>' }
      }
    );

    fireEvent.change(screen.getByPlaceholderText('Ingrese su contraseña'), {
      target: { value: 'password123' }
    });

    // Submit the form
    fireEvent.click(screen.getByText('Iniciar sesión', { selector: 'button' }));

    // Check if loginUser was called with correct data
    await waitFor(() => {
      expect(loginUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      });
    });
  });

  it('handles login error correctly', async () => {
    // Mock login error
    (loginUser as jest.Mock).mockRejectedValueOnce(
      new Error('Invalid credentials')
    );

    render(<Login />);

    // Fill in the form
    fireEvent.change(
      screen.getByPlaceholderText('Ingrese su correo electronico'),
      {
        target: { value: '<EMAIL>' }
      }
    );

    fireEvent.change(screen.getByPlaceholderText('Ingrese su contraseña'), {
      target: { value: 'wrong-password' }
    });

    // Submit the form
    fireEvent.click(screen.getByText('Iniciar sesión', { selector: 'button' }));

    // Check if loginUser was called
    await waitFor(() => {
      expect(loginUser).toHaveBeenCalled();
    });
  });
});
