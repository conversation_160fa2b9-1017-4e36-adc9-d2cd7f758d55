'use client';

import { useState, useEffect, useTransition } from 'react'; // Import useTransition
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { getAvailableSlots } from '../actions/getAvailableSlots'; // Import the action
// Removed getPatients import
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { CalendarIcon, Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import React from 'react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command';

// Define the schema for appointment data
const appointmentSchema = z.object({
  patientId: z.string({
    required_error: 'El paciente es requerido'
  }),
  doctorId: z.string({
    required_error: 'El médico es requerido'
  }),
  date: z
    .date({
      required_error: 'La fecha es requerida'
    })
    .refine(
      (date) => {
        // Ensure date is not in the past (compared to today)
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return date >= today;
      },
      {
        message: 'No se pueden seleccionar fechas pasadas'
      }
    ),
  startTime: z.string({
    required_error: 'La hora de inicio es requerida'
  }),
  duration: z.enum(['15', '30', '45', '60'], {
    required_error: 'La duración es requerida'
  }),
  // Removed appointmentType from schema
  reason: z.string().min(5, 'El motivo debe tener al menos 5 caracteres'),
  notes: z.string().optional()
});

type AppointmentFormData = z.infer<typeof appointmentSchema>;

// TODO: Replace mockDoctors with actual data fetched from API or passed via props
const mockDoctors = [
  { id: '1', name: 'Dr. Juan Pérez', specialty: 'Medicina General' },
  { id: '2', name: 'Dra. María González', specialty: 'Cardiología' },
  { id: '3', name: 'Dr. Carlos Rodríguez', specialty: 'Pediatría' },
  { id: '4', name: 'Dra. Ana Martínez', specialty: 'Dermatología' }
];

// Predefined fallback time slots
const PREDEFINED_FALLBACK_SLOTS = [
  '09:00',
  '09:30',
  '10:00',
  '10:30',
  '11:00',
  '11:30',
  '14:00',
  '14:30',
  '15:00',
  '15:30',
  '16:00',
  '16:30'
];

interface AppointmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  // Note: The parent component using this form should transform
  // AppointmentFormData into the structure expected by createAppointment action
  // before calling it in the onSubmit handler.
  onSubmit: (data: AppointmentFormData) => Promise<void>;
  patients: Array<{ id: string; name: string }>;
  initialPatientId?: string;
  initialDate?: Date | null;
  initialTime?: string;
}

// Custom DatePicker component that doesn't close when clicking
const DatePicker = ({
  selected,
  onSelect
}: {
  selected: Date | undefined;
  onSelect: (date: Date | undefined) => void;
}) => {
  // Use March 2025 as the default month
  const [month, setMonth] = useState<Date>(new Date(2025, 2, 1));

  useEffect(() => {
    // If a date is selected, update the month view to show that month
    if (selected) {
      setMonth(new Date(selected.getFullYear(), selected.getMonth(), 1));
    }
  }, [selected]);

  // Get today's date with time set to midnight
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return (
    <div onClick={(e) => e.stopPropagation()}>
      <Calendar
        mode="single"
        selected={selected}
        onSelect={onSelect}
        defaultMonth={month}
        month={month}
        onMonthChange={setMonth}
        disabled={(date) => date < today} // Disable dates before today
        initialFocus
      />
    </div>
  );
};

export function AppointmentForm({
  isOpen,
  onClose,
  onSubmit,
  patients,
  initialPatientId,
  initialDate,
  initialTime
}: AppointmentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State for comboboxes
  const [openPatientCombobox, setOpenPatientCombobox] = useState(false);
  const [openDoctorCombobox, setOpenDoctorCombobox] = useState(false);

  // State for available slots
  const [availableSlots, setAvailableSlots] = useState<string[]>([]);
  const [isLoadingSlots, startSlotsTransition] = useTransition(); // Use transition for loading
  const [slotsError, setSlotsError] = useState<string | null>(null);

  // Removed patient list state

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<AppointmentFormData>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: {
      patientId: initialPatientId || '',
      doctorId: '',
      // Removed appointmentType from defaultValues
      duration: '30' as '15' | '30' | '45' | '60',
      reason: '',
      notes: ''
    }
  });

  // Set initial date and time if provided
  React.useEffect(() => {
    if (initialDate) {
      setValue('date', initialDate);
    }
    if (initialTime) {
      setValue('startTime', initialTime);
    }
  }, [initialDate, initialTime, setValue]);

  const selectedDate = watch('date');
  const selectedDoctorId = watch('doctorId');
  const selectedPatientId = watch('patientId');

  // Effect to fetch available slots when doctor or date changes
  useEffect(() => {
    // Reset slots and time when dependencies change
    setAvailableSlots([]);
    setSlotsError(null);
    setValue('startTime', ''); // Reset selected time

    if (selectedDoctorId && selectedDate) {
      // Format date to YYYY-MM-DD
      const formattedDate = format(selectedDate, 'yyyy-MM-dd');

      startSlotsTransition(async () => {
        try {
          const response = await getAvailableSlots(
            selectedDoctorId,
            formattedDate
          );
          if (response && response.slots) {
            setAvailableSlots(response.slots);
            if (response.slots.length === 0) {
              setSlotsError('No hay horarios disponibles para esta fecha.');
            }
          } else {
            setAvailableSlots([]);
            setSlotsError('No se pudieron obtener los horarios.');
          }
        } catch (error) {
          console.error('Failed to fetch slots:', error);
          const errorMessage =
            error instanceof Error
              ? error.message
              : 'Error al cargar horarios.';
          setSlotsError(errorMessage);
          setAvailableSlots([]);
        }
      });
    }
  }, [selectedDoctorId, selectedDate, setValue, startSlotsTransition]);

  // Removed patient fetching useEffect

  // Function to handle form submission (Restored)
  const handleFormSubmit = async (data: AppointmentFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data); // Parent component handles the actual API call
      toast.success('Cita agendada exitosamente');
      reset({
        // Reset form preserving initial values if they exist
        patientId: initialPatientId || '',
        doctorId: '',
        // Removed appointmentType from reset
        duration: '30',
        reason: '',
        notes: '',
        date: initialDate || undefined, // Keep initial date if provided
        startTime: initialTime || '' // Keep initial time if provided
      });
      onClose();
    } catch (error) {
      console.error('Error submitting appointment form:', error);
      toast.error(
        error instanceof Error ? error.message : 'Error al agendar la cita'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Removed custom patient filter

  // Removed getAvailableTimeSlots function

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Agendar Nueva Cita Médica
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="grid gap-2">
              <Label htmlFor="patientId">Paciente *</Label>
              <Popover
                open={openPatientCombobox}
                onOpenChange={setOpenPatientCombobox}
                modal={true}
              >
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={openPatientCombobox}
                    className="w-full justify-between"
                  >
                    {selectedPatientId
                      ? patients.find(
                          (patient) => patient.id === selectedPatientId
                        )?.name
                      : 'Seleccione un paciente...'}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder="Buscar paciente..." />
                    <CommandList>
                      <CommandEmpty>No se encontraron pacientes.</CommandEmpty>
                      <CommandGroup heading="Pacientes">
                        {patients.map((patient) => (
                          <CommandItem
                            key={patient.id}
                            value={patient.name}
                            onSelect={() => {
                              setValue('patientId', patient.id);
                              setOpenPatientCombobox(false);
                            }}
                          >
                            <Check
                              className={cn(
                                'mr-2 h-4 w-4',
                                selectedPatientId === patient.id
                                  ? 'opacity-100'
                                  : 'opacity-0'
                              )}
                            />
                            {patient.name}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              {errors.patientId && (
                <p className="text-xs text-red-500">
                  {errors.patientId.message}
                </p>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="doctorId">Médico *</Label>
              <Popover
                open={openDoctorCombobox}
                onOpenChange={setOpenDoctorCombobox}
                modal={true}
              >
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={openDoctorCombobox}
                    className="w-full justify-between"
                  >
                    {selectedDoctorId
                      ? mockDoctors.find(
                          (doctor) => doctor.id === selectedDoctorId
                        )?.name
                      : 'Seleccione un médico...'}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder="Buscar médico..." />
                    <CommandList>
                      <CommandEmpty>No se encontraron médicos.</CommandEmpty>
                      <CommandGroup>
                        {mockDoctors.map((doctor) => (
                          <CommandItem
                            key={doctor.id}
                            value={doctor.name}
                            onSelect={() => {
                              setValue('doctorId', doctor.id);
                              setOpenDoctorCombobox(false);
                            }}
                          >
                            <Check
                              className={cn(
                                'mr-2 h-4 w-4',
                                selectedDoctorId === doctor.id
                                  ? 'opacity-100'
                                  : 'opacity-0'
                              )}
                            />
                            {doctor.name} - {doctor.specialty}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              {errors.doctorId && (
                <p className="text-xs text-red-500">
                  {errors.doctorId.message}
                </p>
              )}
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="date">Fecha *</Label>
            <div className="flex w-full items-center space-x-2">
              <Popover modal={true}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !selectedDate && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? (
                      format(selectedDate, 'PPP', { locale: es })
                    ) : (
                      <span>Seleccione una fecha</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="z-[500] w-auto p-0">
                  <DatePicker
                    selected={selectedDate}
                    onSelect={(date) => date && setValue('date', date)}
                  />
                </PopoverContent>
              </Popover>
            </div>
            {errors.date && (
              <p className="text-xs text-red-500">{errors.date.message}</p>
            )}
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="grid gap-2">
              <Label htmlFor="startTime">Hora *</Label>
              <Select
                onValueChange={(value) => setValue('startTime', value)}
                defaultValue={initialTime}
                disabled={
                  !selectedDate || // Need date selected
                  !selectedDoctorId || // Need doctor selected
                  isLoadingSlots // Disable only while loading slots
                }
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={
                      isLoadingSlots
                        ? 'Cargando horarios...'
                        : !selectedDate || !selectedDoctorId
                        ? 'Seleccione médico y fecha'
                        : availableSlots.length > 0 && !slotsError // API slots available and no error
                        ? 'Seleccione una hora'
                        : 'Seleccione hora (pred.)' // Fallback (error or no API slots)
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingSlots ? (
                    <div className="p-2 text-center text-sm text-muted-foreground">
                      Cargando...
                    </div>
                  ) : (
                    <>
                      {/* Show API slots if available and no error */}
                      {availableSlots.length > 0 &&
                        !slotsError &&
                        availableSlots.map((time) => (
                          <SelectItem key={`api-${time}`} value={time}>
                            {time}
                          </SelectItem>
                        ))}
                      {/* Show Fallback slots if no API slots OR if there is an error */}
                      {(availableSlots.length === 0 || slotsError) &&
                        PREDEFINED_FALLBACK_SLOTS.map((time) => (
                          <SelectItem key={`fallback-${time}`} value={time}>
                            {time} (pred.)
                          </SelectItem>
                        ))}
                      {/* Display error message at the bottom if it exists */}
                      {slotsError && (
                        <div className="mt-1 border-t p-2 text-center text-xs text-red-500">
                          Error: {slotsError}. Mostrando horarios predefinidos.
                        </div>
                      )}
                    </>
                  )}
                </SelectContent>
              </Select>
              {/* Display slot fetching error if it exists and there's no form validation error */}
              {/* Error message is now shown inside SelectContent */}
              {/* Display form validation error */}
              {errors.startTime && (
                <p className="text-xs text-red-500">
                  {errors.startTime.message}
                </p>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="duration">Duración *</Label>
              <Select
                onValueChange={(value) =>
                  setValue('duration', value as '15' | '30' | '45' | '60')
                }
                defaultValue="30"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccione la duración" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15">15 minutos</SelectItem>
                  <SelectItem value="30">30 minutos</SelectItem>
                  <SelectItem value="45">45 minutos</SelectItem>
                  <SelectItem value="60">60 minutos</SelectItem>
                </SelectContent>
              </Select>
              {errors.duration && (
                <p className="text-xs text-red-500">
                  {errors.duration.message}
                </p>
              )}
            </div>
          </div>

          {/* Removed Tipo de Consulta field */}

          <div className="grid gap-2">
            <Label htmlFor="reason">Motivo de la Consulta *</Label>
            <Textarea
              id="reason"
              {...register('reason')}
              placeholder="Describa brevemente el motivo de la consulta"
              className="min-h-[80px]"
            />
            {errors.reason && (
              <p className="text-xs text-red-500">{errors.reason.message}</p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="notes">Notas Adicionales (opcional)</Label>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="Información adicional relevante para la consulta"
              className="min-h-[80px]"
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                reset();
                onClose();
              }}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Agendando...' : 'Agendar Cita'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
