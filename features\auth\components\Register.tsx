'use client';

import React, { useState } from 'react';
import LoginGoogle from './GoogleButton';
import { PATHROUTES } from '../../../helpers';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { RegisterSchema, RegisterSchemaType } from '../lib/zod/register.schema';
import { registerAction } from '../actions/register';
import useOnboardingStore from '../../../features/onboarding/context/onboardingStore';

const Register = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { setStepData } = useOnboardingStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<RegisterSchemaType>({
    resolver: zodResolver(RegisterSchema),
    defaultValues: {
      name: '',
      last_name: '',
      email: '',
      password: '',
      confirmPassword: ''
    }
  });

  const onSubmit = async (data: RegisterSchemaType) => {
    try {
      console.log('Datos del formulario originales:', data);

      setLoading(true);
      const result = await registerAction(data);
      console.log('Resultado del registro:', result);

      // Guardar información relevante en el store de onboarding
      if (result && result.user) {
        setStepData('userId', result.user.id);
        setStepData('name', `${result.user.name} ${result.user.last_name}`);
      }

      reset();
      toast.success('Usuario registrado con éxito');
      // Redirigir al usuario a la primera etapa del onboarding
      router.push(PATHROUTES.ONBOARDING);
    } catch (err) {
      console.error('Error al registrar:', err);
      if (err && typeof err === 'object' && 'message' in err) {
        toast.error(`Error: ${err.message}`);
      } else {
        toast.error('Hubo un error al registrar el usuario');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-[500px] space-y-5 border-2 bg-white px-5 py-3 shadow-lg">
      <p className="text-center text-[28px] font-bold text-bluePrimary">
        Registrate gratis
      </p>

      <LoginGoogle />

      <p className="text-textColor relative text-center text-xl font-semibold before:absolute before:-left-[1px] before:top-1/2 before:w-[120px] before:bg-gray-400 before:content-[''] after:-right-[1px] after:top-1/2 after:bg-gray-400 after:content-[''] md:before:h-[1px] md:after:absolute md:after:h-[1px] md:after:w-[120px]">
        o ingresa tus datos
      </p>

      <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-col">
          <label htmlFor="name">Nombre</label>
          <input
            className="border-red border-2 border-slate-300 bg-inputbg p-2"
            type="text"
            placeholder="Ingrese su nombre"
            {...register('name')}
          />
          {errors.name && (
            <p className="text-xs text-red-500">{errors.name.message}</p>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="last_name">Apellido</label>
          <input
            className="border-red border-2 border-slate-300 bg-inputbg p-2"
            type="text"
            placeholder="Ingrese su apellido"
            {...register('last_name')}
          />
          {errors.last_name && (
            <p className="text-xs text-red-500">{errors.last_name.message}</p>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="email">Correo Electronico</label>
          <input
            className="border-red border-2 border-slate-300 bg-inputbg p-2"
            type="email"
            placeholder="Ingrese su correo electronico"
            {...register('email')}
          />
          {errors.email && (
            <p className="text-xs text-red-500">{errors.email.message}</p>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="password">Contraseña</label>
          <input
            className="border-red border-2 border-slate-300 bg-inputbg p-2"
            type="password"
            placeholder="Ingrese su contraseña"
            {...register('password')}
          />
          {errors.password && (
            <p className="text-xs text-red-500">{errors.password.message}</p>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="confirmPassword">Confirmar contraseña</label>
          <input
            className="border-red border-2 border-slate-300 bg-inputbg p-2"
            type="password"
            placeholder="Confirme su contraseña"
            {...register('confirmPassword')}
          />
          {errors.confirmPassword && (
            <p className="text-xs text-red-500">
              {errors.confirmPassword.message}
            </p>
          )}
        </div>

        <button
          className="flex w-full justify-center rounded-xl bg-bluePrimary py-2 font-bold text-white"
          type="submit"
          disabled={loading}
          onClick={() => console.log('Botón de registro clickeado')}
        >
          {loading ? 'Cargando...' : 'Crear cuenta'}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor"
            className="ml-4 size-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M8.25 9V5.25A2.25 2.25 0 0 1 10.5 3h6a2.25 2.25 0 0 1 2.25 2.25v13.5A2.25 2.25 0 0 1 16.5 21h-6a2.25 2.25 0 0 1-2.25-2.25V15M12 9l3 3m0 0-3 3m3-3H2.25"
            />
          </svg>
        </button>
      </form>
      <p className="text-center">
        ¿Ya tienes una cuenta?{' '}
        <Link className="text-bluePrimary" href={PATHROUTES.LOGIN}>
          Inicia Sesión
        </Link>
      </p>
    </div>
  );
};

export default Register;
