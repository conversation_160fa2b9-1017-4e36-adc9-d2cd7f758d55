'use server';

import { api } from '../../../lib/axios';
import { MedicalEventResponse } from './createMedicalEvent';

// Define the query parameters
export interface MedicalEventQueryParams {
  patientId?: string;
  physicianId?: string;
  page?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

// Define the paginated response
export interface PaginatedMedicalEventsResponse {
  data: MedicalEventResponse[];
  meta: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

/**
 * Fetches medical events with optional filtering
 * @param params Query parameters
 * @returns Paginated medical events
 */
export async function getMedicalEvents(
  params: MedicalEventQueryParams = {}
): Promise<PaginatedMedicalEventsResponse> {
  try {
    // Format the parameters for the API
    const queryParams = {
      patient_id: params.patientId,
      physician_id: params.physicianId,
      page: params.page,
      pageSize: params.pageSize,
      orderBy: params.orderBy,
      orderDirection: params.orderDirection
    };

    // Make the API request
    const response = await api.get<PaginatedMedicalEventsResponse>(
      '/medical-events',
      { params: queryParams }
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching medical events:', error);

    // Handle error
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(error.message as string);
    }

    throw new Error('Failed to fetch medical events');
  }
}
