'use client';

import {
  Search,
  UserRoundPlus,
  ArrowUpDown,
  SortAsc,
  SortDesc,
  Calendar,
  Clock,
  X
} from 'lucide-react';
import { TbSortAscendingLetters } from 'react-icons/tb';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { SearchAndFilterProps, SortCriteria } from '../types/dashboardTypes';

export function SearchAndFilterBar({
  searchTerm,
  onSearchChange,
  sortBy,
  onSortChange,
  onAddPatient
}: SearchAndFilterProps) {
  const handleSort = (criteria: SortCriteria) => {
    onSortChange(criteria);
  };

  const getSortLabel = (criteria: SortCriteria | null) => {
    switch (criteria) {
      case 'name':
        return 'Alfabéticamente';
      case 'age-asc':
        return 'Mayor a menor edad';
      case 'age-desc':
        return 'Menor a mayor edad';
      case 'recent':
        return 'Más recientes';
      default:
        return null;
    }
  };

  const getSortIcon = (
    criteria: SortCriteria | null,
    isActive: boolean = false
  ) => {
    const iconClass = isActive ? 'h-4 w-4 text-blue-700' : 'h-4 w-4';
    switch (criteria) {
      case 'name':
        return <TbSortAscendingLetters className={iconClass} />;
      case 'age-asc':
        return <SortDesc className={iconClass} />;
      case 'age-desc':
        return <SortAsc className={iconClass} />;
      case 'recent':
        return <Clock className={iconClass} />;
      default:
        return null;
    }
  };

  const clearSort = () => {
    onSortChange(null);
  };

  return (
    <div className="flex items-center px-6 py-4 bg-gray-50 border-b border-gray-200">
      {/* Search Input */}
      <div className="relative w-80">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="Buscar"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
          style={{ backgroundColor: '#FBFBFB' }}
        />
      </div>

      {/* Sort/Filter Area */}
      <div className="ml-4 flex items-center gap-2">
        {!sortBy ? (
          /* Sort Dropdown when no filter is active */
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="default"
                className="flex items-center gap-2 rounded-lg border-gray-300 hover:bg-gray-50"
                style={{ backgroundColor: '#FBFBFB' }}
              >
                <ArrowUpDown className="h-4 w-4" />
                Ordenar
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              side="bottom"
              align="center"
              className="w-56"
              style={{ backgroundColor: '#FBFBFB' }}
            >
              <DropdownMenuItem
                onSelect={() => handleSort('name')}
                className="flex items-center gap-2 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700"
              >
                <TbSortAscendingLetters className="h-4 w-4" />
                Alfabéticamente
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => handleSort('age-asc')}
                className="flex items-center gap-2 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700"
              >
                <SortDesc className="h-4 w-4" />
                Mayor a menor edad
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => handleSort('age-desc')}
                className="flex items-center gap-2 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700"
              >
                <SortAsc className="h-4 w-4" />
                Menor a mayor edad
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => handleSort('recent')}
                className="flex items-center gap-2 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700"
              >
                <Clock className="h-4 w-4" />
                Más recientes
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          /* Active Filter Display - replaces the Sort button */
          <>
            <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg">
              {getSortIcon(sortBy, true)}
              <span className="text-sm text-blue-700">
                {getSortLabel(sortBy)}
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={clearSort}
              className="text-red-500 border-red-200 hover:bg-red-50 hover:border-red-300"
            >
              <X className="h-4 w-4 mr-1" />
              Eliminar filtro
            </Button>
          </>
        )}
      </div>

      {/* Add Patient Button - pushed to the right */}
      <div className="ml-auto">
        <Button
          variant="outline"
          size="default"
          className="flex items-center gap-2 rounded-lg border-gray-300 hover:bg-gray-50"
          style={{ backgroundColor: '#FBFBFB' }}
          onClick={onAddPatient}
        >
          <UserRoundPlus className="h-4 w-4 text-gray-500" />
          Agregar paciente
        </Button>
      </div>
    </div>
  );
}
