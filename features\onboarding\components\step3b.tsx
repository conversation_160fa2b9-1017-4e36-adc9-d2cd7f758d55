'use client';
import { useEffect, useState } from 'react';
import useOnboardingStore from '../context/onboardingStore';

interface Step3bProps {
  onValidation: (isValid: boolean) => void;
}

const Step3b = ({ onValidation }: Step3bProps) => {
  const cantidadPacientes = [
    'Ingrese el numero de pacientes',
    '1-2',
    '3-5',
    '5-10',
    '10-20',
    '20-50',
    'Más de 50'
  ];

  const [error, setError] = useState('');

  const { stepData, setStepData } = useOnboardingStore();
  const userName = stepData.name || '';

  useEffect(() => {
    const isValidName = /^[a-zA-Z\s]+$/.test(stepData.organizacion || '');
    const isValidPacientes =
      stepData.pacientes && stepData.pacientes !== 'default';

    if (!stepData.organizacion || !isValidPacientes) {
      setError('Por favor, complete todos los campos');
      onValidation(false);
    } else if (!isValidName || stepData.organizacion.length < 3) {
      setError('El nombre de la organización debe tener al menos 3 caracteres');
      onValidation(false);
    } else {
      setError('');
      onValidation(true);
    }
  }, [stepData.organizacion, stepData.pacientes, onValidation]);

  return (
    <div className="flex w-full flex-col space-y-6">
      <h1 className="text-center text-2xl font-bold text-[#487FFA]">
        {userName
          ? `${userName}, contanos sobre tu organización`
          : 'Contanos sobre tu organización'}
      </h1>

      <p className="text-start text-base text-[#5F5F5F] sm:text-sm">
        Configuremos tu perfil, puedes cambiarlo después
      </p>

      <div className="flex w-full flex-col space-y-6">
        <div className="flex flex-col space-y-2">
          <label htmlFor="organizacion" className="text-sm text-[#5F5F5F]">
            Nombre de la organización
          </label>
          <input
            type="text"
            id="organizacion"
            value={stepData.organizacion || ''}
            onChange={(e) => {
              setStepData('organizacion', e.target.value);
            }}
            className="w-full  border border-gray-300 bg-white p-2 text-sm focus:border-[#487FFA] focus:outline-none focus:ring focus:ring-[#487FFA]"
            placeholder="Ingrese el nombre de la organización"
          />
        </div>

        <div className="flex flex-col space-y-2">
          <label htmlFor="pacientes" className="text-sm text-[#5F5F5F]">
            Ingrese el número de pacientes
          </label>
          <select
            name="pacientes"
            id="pacientes"
            value={stepData.pacientes || 'default'}
            onChange={(e) => {
              setStepData('pacientes', e.target.value);
            }}
            className="w-full  border border-gray-300 bg-white p-2 text-sm focus:border-[#487FFA] focus:outline-none focus:ring focus:ring-[#487FFA]"
          >
            {cantidadPacientes.map((cantidad, index) => (
              <option
                key={index}
                value={
                  cantidad === 'Ingrese el numero de pacientes' ? '' : cantidad
                }
              >
                {cantidad}
              </option>
            ))}
          </select>
        </div>

        {error && <p className="text-sm text-red-500">{error}</p>}
      </div>
    </div>
  );
};

export default Step3b;
