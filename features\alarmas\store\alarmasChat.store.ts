import { create } from 'zustand';
import { Message } from '../../../types/chatbot-alarmas';

interface AlarmasChatState {
  messages: Message[];
  loading: boolean;
  error: string | null;
  sessionId: string | null;
  sessionStatus: 'inactive' | 'active' | 'ended';
  connectionStatus: 'connected' | 'disconnected' | 'reconnecting';
  addMessage: (message: Message) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSessionId: (sessionId: string) => void;
  setSessionStatus: (status: 'inactive' | 'active' | 'ended') => void;
  setConnectionStatus: (
    status: 'connected' | 'disconnected' | 'reconnecting'
  ) => void;
  resetMessages: () => void;
  clearError: () => void;
}

export const useAlarmasChatStore = create<AlarmasChatState>((set) => ({
  messages: [],
  loading: false,
  error: null,
  sessionId: null,
  sessionStatus: 'inactive',
  connectionStatus: 'disconnected',
  addMessage: (message) =>
    set((state) => ({
      messages: [...state.messages, { ...message, timestamp: new Date() }]
    })),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  setSessionId: (sessionId) => set({ sessionId }),
  setSessionStatus: (status) => set({ sessionStatus: status }),
  setConnectionStatus: (status) => set({ connectionStatus: status }),
  resetMessages: () =>
    set({
      messages: [],
      error: null,
      sessionId: null,
      sessionStatus: 'inactive',
      connectionStatus: 'disconnected'
    }),
  clearError: () => set({ error: null })
}));
