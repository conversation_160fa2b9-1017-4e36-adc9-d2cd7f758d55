'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter // Optional: if you need specific footer actions in the modal itself
} from '@/components/ui/dialog';
import PatientCreate from './PatientCreate'; // The form component
import { getPatientDetails } from '../actions/getPatientDetails';
import { MedicalPatientDto } from '../types/patient';
import { toast } from 'sonner';
import FormCardSkeleton from '@/components/form-card-skeleton'; // Assuming a skeleton loader exists

interface EditPatientModalProps {
  isOpen: boolean;
  onClose: () => void;
  patientId: string | null;
}

export const EditPatientModal: React.FC<EditPatientModalProps> = ({
  isOpen,
  onClose,
  patientId
}) => {
  const [patientData, setPatientData] = useState<MedicalPatientDto | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && patientId) {
      const fetchDetails = async () => {
        setIsLoading(true);
        setError(null);
        setPatientData(null); // Reset previous data
        try {
          const data = await getPatientDetails(patientId);
          setPatientData(data);
        } catch (err) {
          console.error('Failed to fetch patient details:', err);
          const errorMessage =
            err instanceof Error
              ? err.message
              : 'Error al cargar los datos del paciente.';
          setError(errorMessage);
          toast.error(errorMessage);
          // Optionally close the modal on error, or let the user close it
          // onClose();
        } finally {
          setIsLoading(false);
        }
      };
      fetchDetails();
    } else {
      // Reset state if modal is closed or patientId is null
      setPatientData(null);
      setIsLoading(false);
      setError(null);
    }
  }, [isOpen, patientId]); // Rerun effect when isOpen or patientId changes

  // Handle modal state changes (e.g., closing via 'x' or overlay click)
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose(); // Call the onClose prop when the dialog requests to be closed
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[80%] md:max-w-[70%] lg:max-w-[60%] xl:max-w-[900px]">
        {' '}
        {/* Adjust width as needed */}
        <DialogHeader>
          <DialogTitle>Editar Paciente</DialogTitle>
          <DialogDescription>
            Modifica los datos del paciente. Los cambios se guardarán al hacer
            clic en &apos;Actualizar Paciente&apos;.
          </DialogDescription>
        </DialogHeader>
        {isLoading && <FormCardSkeleton />} {/* Show skeleton loader */}
        {error && !isLoading && (
          <div className="rounded border border-red-200 bg-red-50 p-4 text-red-600">
            Error: {error}
          </div>
        )}
        {!isLoading && !error && patientData && (
          <PatientCreate
            isEditMode={true}
            patientId={patientId!} // Assert patientId is non-null here as it's checked in useEffect
            initialData={patientData}
            onClose={onClose} // Pass the onClose handler to the form
          />
        )}
        {/* Optional: Add a footer inside the modal if needed,
            otherwise the form's submit button handles closure via onClose prop */}
        {/* <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancelar</Button>
        </DialogFooter> */}
      </DialogContent>
    </Dialog>
  );
};
