import { ClinicalHistory } from '../types';

// Sample mock clinical history for a patient
export const mockClinicalHistory: ClinicalHistory = {
  patientId: '1',
  appointments: [
    {
      id: 'appt-1',
      date: '2023-10-15',
      startTime: '09:00',
      endTime: '09:30',
      reason: 'Consulta de rutina',
      status: 'completed',
      doctorName: 'Dr. <PERSON>',
      notes: 'Paciente acudió a tiempo. Sin complicaciones.'
    },
    {
      id: 'appt-2',
      date: '2023-11-20',
      startTime: '10:00',
      endTime: '10:30',
      reason: 'Seguimiento de tratamiento',
      status: 'completed',
      doctorName: 'Dr. <PERSON>',
      notes: 'Paciente reporta mejoría en síntomas.'
    },
    {
      id: 'appt-3',
      date: '2024-01-10',
      startTime: '11:00',
      endTime: '11:30',
      reason: 'Control de presión arterial',
      status: 'scheduled',
      doctorName: '<PERSON><PERSON><PERSON> <PERSON>'
    }
  ],
  vitalSigns: [
    {
      id: 'vs-1',
      date: '2023-10-15',
      temperature: 36.7,
      bloodPressureSystolic: 120,
      bloodPressureDiastolic: 80,
      heartRate: 72,
      respiratoryRate: 16,
      oxygenSaturation: 98,
      weight: 70.5,
      height: 175,
      bmi: 23.0
    },
    {
      id: 'vs-2',
      date: '2023-11-20',
      temperature: 36.5,
      bloodPressureSystolic: 118,
      bloodPressureDiastolic: 78,
      heartRate: 70,
      respiratoryRate: 15,
      oxygenSaturation: 99,
      weight: 71.0,
      height: 175,
      bmi: 23.2
    }
  ],
  studies: [
    {
      id: 'study-1',
      date: '2023-10-20',
      type: 'Análisis de sangre',
      description: 'Hemograma completo',
      requestedBy: 'Dr. Juan Pérez',
      results: 'Valores dentro de rangos normales',
      fileUrls: ['/studies/hemograma-20231020.pdf']
    },
    {
      id: 'study-2',
      date: '2023-11-25',
      type: 'Radiografía',
      description: 'Radiografía de tórax',
      requestedBy: 'Dr. Juan Pérez',
      results: 'Sin hallazgos significativos',
      fileUrls: ['/studies/radiografia-20231125.jpg']
    }
  ],
  progressNotes: [
    {
      id: 'note-1',
      date: '2023-10-15',
      doctorName: 'Dr. Juan Pérez',
      content:
        'Paciente acude por control de rutina. Refiere sentirse bien, sin molestias significativas. Se realizan estudios de control.'
    },
    {
      id: 'note-2',
      date: '2023-11-20',
      doctorName: 'Dr. Juan Pérez',
      content:
        'Paciente acude para seguimiento. Refiere mejoría en su estado general. Se revisan resultados de estudios previos, todos dentro de parámetros normales.'
    }
  ],
  physicalExaminations: [
    {
      id: 'exam-1',
      date: '2023-10-15',
      generalAppearance: 'Buen estado general',
      skin: 'Sin lesiones',
      head: 'Normocéfalo',
      eyes: 'Pupilas isocóricas y normorreactivas',
      ears: 'Conductos auditivos permeables',
      nose: 'Sin alteraciones',
      mouth: 'Mucosa oral hidratada',
      neck: 'Sin adenomegalias',
      chest: 'Simétrico',
      lungs: 'Murmullo vesicular presente, sin ruidos agregados',
      heart: 'Ruidos cardíacos rítmicos, sin soplos',
      abdomen: 'Blando, depresible, no doloroso',
      extremities: 'Sin edema, pulsos presentes',
      neurologicalExam: 'Sin alteraciones'
    }
  ],
  diagnoses: [
    {
      id: 'diag-1',
      date: '2023-10-15',
      description: 'Hipertensión arterial controlada',
      icdCode: 'I10',
      type: 'primary',
      status: 'active'
    }
  ],
  labRadiologyRequests: [
    {
      id: 'req-1',
      date: '2023-10-15',
      type: 'laboratory',
      description: 'Hemograma completo, perfil lipídico, glucemia',
      status: 'completed',
      results: 'Resultados dentro de parámetros normales',
      fileUrls: ['/lab-results/lab-20231020.pdf']
    }
  ],
  procedures: [
    {
      id: 'proc-1',
      date: '2023-11-20',
      name: 'Electrocardiograma',
      description: 'ECG de 12 derivaciones',
      performedBy: 'Dr. Juan Pérez',
      notes: 'Ritmo sinusal, sin alteraciones significativas'
    }
  ],
  medications: [
    {
      id: 'med-1',
      name: 'Enalapril',
      dosage: '10mg',
      route: 'oral',
      frequency: 'una vez al día',
      startDate: '2023-10-15',
      status: 'active',
      prescribedBy: 'Dr. Juan Pérez',
      notes: 'Tomar por la mañana'
    }
  ],
  medicalInstructions: [
    {
      id: 'inst-1',
      date: '2023-10-15',
      content:
        'Mantener dieta baja en sodio. Realizar actividad física moderada al menos 30 minutos, 3 veces por semana.',
      doctorName: 'Dr. Juan Pérez'
    }
  ],
  medicalTreatments: [
    {
      id: 'treat-1',
      date: '2023-10-15',
      description: 'Tratamiento antihipertensivo con Enalapril 10mg diario',
      duration: '3 meses, reevaluar'
    }
  ],
  vaccinations: [
    {
      id: 'vac-1',
      date: '2023-09-01',
      name: 'Vacuna contra la influenza',
      manufacturer: 'Sanofi Pasteur',
      administeredBy: 'Enfermera Lucía Rodríguez',
      site: 'Brazo izquierdo'
    }
  ],
  allergies: [
    {
      id: 'allergy-1',
      allergen: 'Penicilina',
      reaction: 'Erupción cutánea',
      severity: 'moderate',
      dateIdentified: '2015-05-10',
      status: 'active'
    }
  ],
  pastMedicalHistory: [
    {
      id: 'pmh-1',
      condition: 'Apendicitis',
      diagnosisDate: '2010-03-15',
      resolutionDate: '2010-03-20',
      status: 'resolved',
      notes: 'Apendicectomía sin complicaciones'
    }
  ],
  nonPathologicalHistory: [
    {
      id: 'nph-1',
      category: 'smoking',
      description: 'Ex-fumador, 10 cigarrillos/día durante 5 años',
      notes: 'Dejó de fumar hace 8 años'
    },
    {
      id: 'nph-2',
      category: 'exercise',
      description: 'Caminata 30 minutos, 3 veces por semana'
    }
  ],
  familyHistory: [
    {
      id: 'fh-1',
      condition: 'Hipertensión arterial',
      relationship: 'Padre',
      notes: 'Diagnosticado a los 50 años'
    },
    {
      id: 'fh-2',
      condition: 'Diabetes mellitus tipo 2',
      relationship: 'Abuela materna',
      notes: 'Diagnosticada a los 65 años'
    }
  ],
  surgicalHistory: [
    {
      id: 'sh-1',
      procedure: 'Apendicectomía',
      date: '2010-03-15',
      surgeon: 'Dr. Roberto Sánchez',
      hospital: 'Hospital Central'
    }
  ],
  youthHistory: [
    {
      id: 'yh-1',
      category: 'childhood_illness',
      description: 'Varicela',
      age: '8 años'
    }
  ]
};

// Function to get a patient's clinical history
export const getMockClinicalHistory = (patientId: string): ClinicalHistory => {
  // In a real application, this would fetch data from an API
  // For now, we just return the mock data
  return {
    ...mockClinicalHistory,
    patientId
  };
};
