'use client';
import React, { useState } from 'react';
import { Trash } from 'lucide-react';
import useConsultationStore from '../../context/consultationStore';

const Recipes = () => {
  const {
    medications,
    instructions,
    addMedication,
    updateMedication,
    removeMedication,
    setInstructions
  } = useConsultationStore();

  const [inputMedicamento, setInputMedicamento] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddMedication = () => {
    if (inputMedicamento.trim() !== '') {
      addMedication({
        nombre: inputMedicamento,
        dosis: '',
        frecuencia: '',
        duracion: ''
      });
      setInputMedicamento('');
    }
  };

  const saveToBackend = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/recipes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ medications, instructions })
      });

      if (!response.ok) {
        throw new Error('Error al guardar la receta');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error al enviar los datos:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mx-auto w-full max-w-2xl rounded-lg border border-gray-300 bg-white  p-4">
      <h1 className="mb-2 text-sm font-semibold text-gray-800">
        Receta de medicamentos
      </h1>

      <div className="mb-4 flex gap-2">
        <input
          type="text"
          placeholder="Indicar medicamento..."
          value={inputMedicamento}
          onChange={(e) => setInputMedicamento(e.target.value)}
          className="w-full rounded border bg-white p-2"
        />
        <button
          onClick={handleAddMedication}
          className="rounded bg-blue-500 px-2 py-2 text-white"
        >
          Agregar
        </button>
      </div>

      {medications.length > 0 && (
        <div className="rounded border p-2">
          <div className="mb-2 grid grid-cols-5 gap-2 font-bold">
            <div>Medicamento</div>
            <div>Dosis</div>
            <div>Frecuencia</div>
            <div>Duración</div>
            <div>Eliminar</div>
          </div>
          {medications.map((med, index) => (
            <div
              key={index}
              className="mb-2 grid grid-cols-5 items-center gap-2"
            >
              <div>{med.nombre}</div>
              <select
                value={med.dosis}
                onChange={(e) =>
                  updateMedication(index, 'dosis', e.target.value)
                }
                onBlur={saveToBackend}
                className="rounded border bg-white p-1"
              >
                <option value="">Seleccionar</option>
                <option value="1/2 tableta">1/2 Tableta</option>
                <option value="1 tableta">1 Tableta</option>
                <option value="2 tabletas">2 Tabletas</option>
                <option value="1 ampolleta">1 Ampolleta</option>
                <option value="1 cápsula">1 Cápsula</option>
                <option value="2 cápsulas">1 Cápsulas</option>
                <option value="1 pastilla">1 Pastilla</option>
                <option value="2 pastillas">1 Pastillas</option>
                <option value="1 cucharada">1 Cucharada</option>
                <option value="1 gota">1 Gota</option>
                <option value="2 gotas">1 Gotas</option>
                <option value="2.5 ML">2.5 ML</option>
                <option value="5 ML">5 ML</option>
                <option value="10 ML">10 ML</option>
              </select>
              <select
                value={med.frecuencia}
                onChange={(e) =>
                  updateMedication(index, 'frecuencia', e.target.value)
                }
                onBlur={saveToBackend}
                className="rounded border bg-white p-1"
              >
                <option value="">Seleccionar</option>
                <option value="Cada 4 horas">Cada 4 horas</option>
                <option value="Cada 6 horas">Cada 6 horas</option>
                <option value="Cada 8 horas">Cada 8 horas</option>
                <option value="Cada 12 horas">Cada 12 horas</option>
                <option value="Cada 24 horas">Cada 24 horas</option>
              </select>

              <select
                value={med.duracion}
                onChange={(e) =>
                  updateMedication(index, 'duracion', e.target.value)
                }
                onBlur={saveToBackend}
                className="rounded border bg-white p-1"
              >
                <option value="">Seleccionar</option>
                <option value="3 dias">3 días</option>
                <option value="5 dias">5 días</option>
                <option value="7 dias">7 días</option>
                <option value="10 dias">10 días</option>
                <option value="15 dias">15 días</option>
                <option value="30 dias">30 días</option>
              </select>
              <button
                onClick={() => removeMedication(index)}
                className="text-red-500"
              >
                <Trash />
              </button>
            </div>
          ))}
        </div>
      )}

      <div className="mt-4">
        <label
          htmlFor="instrucciones"
          className="mb-2 text-sm font-semibold text-gray-800"
        >
          Instrucciones Médicas
        </label>
        <input
          type="text"
          placeholder="Detallar Instrucciones"
          value={instructions}
          onChange={(e) => setInstructions(e.target.value)}
          className="w-full rounded border bg-white p-2"
          onBlur={saveToBackend}
        />
      </div>

      <div className="mt-4 flex justify-end">
        <button
          onClick={saveToBackend}
          className="rounded bg-green-600 px-4 py-2 text-white transition-all hover:bg-green-700"
          disabled={loading}
        >
          {loading ? 'Guardando...' : 'Guardar Receta'}
        </button>
      </div>
    </div>
  );
};

export default Recipes;
