'use server';

import { del } from '@/lib/api';
import { revalidatePath } from 'next/cache';

export async function deletePatientTenant(
  patientId: string
): Promise<{ success: boolean; message?: string }> {
  try {
    // TODO: Add permission check here (e.g., checkPermission(Permissions.DELETE_PATIENT))

    // The 'del' function from apiRequest handles non-ok responses and throws errors.
    // We expect a 204 No Content or similar success response, so the return type T can be 'unknown' or a specific success DTO if the API provides one.
    await del<unknown>(`/patient/${patientId}`);

    // Revalidate the dashboard path to reflect changes
    revalidatePath('/dashboard');

    return {
      success: true,
      message: 'Patient association deleted successfully.'
    };
  } catch (error) {
    console.error('Error in deletePatientTenant server action:', error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : 'An unexpected error occurred.'
    };
  }
}
