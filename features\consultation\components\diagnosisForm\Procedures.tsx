'use client';

import React, { useState } from 'react';
import { Trash } from 'lucide-react';
import useConsultationStore from '../../context/consultationStore';

const Procedures = () => {
  const { procedures, addProcedure, removeProcedure } = useConsultationStore();
  const [procedureInput, setProcedureInput] = useState('');
  const [loading, setLoading] = useState(false);

  const saveToBackend = async (procedure: string) => {
    try {
      setLoading(true);
      const response = await fetch('/api/procedures', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ procedure })
      });

      if (!response.ok) {
        throw new Error('Error al guardar el procedimiento');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error al enviar los datos:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBlur = async () => {
    if (procedureInput.trim() !== '') {
      addProcedure(procedureInput);
      await saveToBackend(procedureInput);
      setProcedureInput('');
    }
  };

  return (
    <div className="mx-auto w-full max-w-2xl rounded-lg border border-gray-300 bg-white  p-4">
      <h1 className="mb-2 text-sm font-semibold text-gray-800">
        Procedimientos
      </h1>
      <div className="mb-4 flex gap-2">
        <input
          type="text"
          placeholder="Indicar Procedimiento..."
          className="w-full rounded border border-[#DCDBDB] bg-white p-2"
          value={procedureInput}
          onChange={(e) => setProcedureInput(e.target.value)}
          onBlur={handleBlur}
        />
        <button
          className="rounded bg-blue-500 px-2 py-2 text-white"
          onClick={handleBlur}
          disabled={loading}
        >
          {loading ? 'Guardando...' : 'Agregar'}
        </button>
      </div>
      <div>
        {procedures.map((procedure, index) => (
          <div
            key={index}
            className="mb-2 flex items-center justify-between rounded border border-[#DCDBDB] bg-gray-100 p-2"
          >
            <p className="font-lg">{procedure}</p>
            <button
              className="text-red-500"
              onClick={() => removeProcedure(procedure)}
            >
              <Trash size={20} />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Procedures;
