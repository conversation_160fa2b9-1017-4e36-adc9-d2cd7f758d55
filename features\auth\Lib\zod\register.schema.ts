import { z } from 'zod';

const emailSchema = z
  .string()
  .min(1, { message: 'Debe ingresar un email' })
  .email({ message: 'Debe ingresar un email válido' });

const passwordSchema = z
  .string()
  .min(6, { message: 'La contraseña debe tener al menos 6 caracteres' })
  .regex(/[A-Z]/, { message: 'Debe contener al menos una letra mayúscula' })
  .regex(/[a-z]/, { message: 'Debe contener al menos una letra minúscula' })
  .regex(/\d/, { message: 'Debe contener al menos un número' })
  .regex(/[@#$!%*?&]/, {
    message: 'Debe contener al menos un carácter especial'
  });

const confirmPasswordSchema = z
  .string()
  .min(1, 'Debes confirmar tu contraseña');

const nameSchema = z
  .string()
  .min(2, { message: 'El nombre debe tener al menos 2 caracteres' })
  .max(25, { message: 'El nombre no puede exceder los 25 caracteres' })
  .regex(/^[a-zA-Z\s]+$/, {
    message: 'El nombre solo debe contener letras y espacios'
  });

const lastNameSchema = z
  .string()
  .min(2, { message: 'El apellido debe tener al menos 2 caracteres' })
  .max(25, { message: 'El apellido no puede exceder los 25 caracteres' })
  .regex(/^[a-zA-Z\s]+$/, {
    message: 'El apellido solo debe contener letras y espacios'
  });

export const RegisterSchema = z
  .object({
    name: nameSchema,
    last_name: lastNameSchema,
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: confirmPasswordSchema
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Las contraseñas no coinciden',
    path: ['confirmPassword']
  });

export type RegisterSchemaType = z.infer<typeof RegisterSchema>;
