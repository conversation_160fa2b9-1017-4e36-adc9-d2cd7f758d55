'use client';
import { Settings } from 'lucide-react';
import { Avatar, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { usePathname, useRouter } from 'next/navigation';
import { useUserStore } from '@/features/auth/store/user.store';
import { useEffect } from 'react';
import { NotificationDropdown } from '@/components/notification-dropdown';

export function AppHeader() {
  const name = useUserStore((state) => state.name);
  const picture = useUserStore((state) => state.picture);
  const router = useRouter();

  useEffect(() => {}, [name, picture]);

  const pathname = usePathname();
  const capitalize = (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1);

  const getPageTitle = () => {
    if (pathname === '/dashboard') return 'Pacientes';
    if (pathname === '/dashboard/new-patient') return 'Agregar paciente';
    if (pathname === '/dashboard/configuracion') return 'Configuración';
    return capitalize(pathname.split('/').pop() || '');
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-gray-50/95 backdrop-blur supports-[backdrop-filter]:bg-gray-50/60">
      <div className="container flex h-14 items-center justify-between !mt-4">
        <h1 className="text-xl font-normal !text-[rgb(128,128,128)]">
          {getPageTitle()}
        </h1>
        <div className="flex items-center gap-2">
          <NotificationDropdown />
          <Button
            variant="ghost"
            size="icon"
            className="h-11 w-11 border-2 border-gray-200 text-gray-500 hover:text-gray-900"
            onClick={() => router.push('/dashboard/configuracion')}
          >
            <Settings className="h-[26px] w-[26px]" />
          </Button>
          <Avatar className="h-11 w-11 rounded-[6px] border-2 border-gray-200">
            <AvatarImage
              className="object-cover"
              src="https://wallpapers.com/images/hd/white-astronaut-cartoon-iphone-dubzue0ncd7tr0m7.jpg"
              alt="User"
              width={500}
              height={500}
            />
          </Avatar>
        </div>
      </div>
    </header>
  );
}
