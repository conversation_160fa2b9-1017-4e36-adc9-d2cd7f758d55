'use server';

import { get } from '../../../lib/api'; // Assuming get is exported from lib/api.ts

// Define the expected response type for available slots
// Adjust this based on the actual API response structure.
// Assuming it returns an array of time strings like "HH:mm".
export interface AvailableSlotsResponse {
  slots: string[]; // e.g., ["09:00", "09:30", "10:00"]
  // Add other fields if the API returns more details
}

/**
 * Fetches available appointment slots for a physician on a specific date.
 * @param physicianId The ID of the physician.
 * @param date The date in 'YYYY-MM-DD' format.
 * @returns A promise resolving to the available slots response.
 */
export async function getAvailableSlots(
  physicianId: string,
  date: string // Expecting 'YYYY-MM-DD' format
): Promise<AvailableSlotsResponse> {
  if (!physicianId) {
    throw new Error('Physician ID is required to fetch available slots.');
  }
  if (!date) {
    throw new Error('Date is required to fetch available slots.');
  }

  // Validate date format if necessary (simple check)
  if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    throw new Error('Invalid date format. Please use YYYY-MM-DD.');
  }

  try {
    // Construct the endpoint URL with query parameters
    const endpoint = `/physicians/${physicianId}/slots?date=${date}`;

    // Call the get function from lib/api
    // Assuming the API returns an object with a 'slots' array: { slots: ["09:00", ...] }
    const response = await get<AvailableSlotsResponse>(endpoint);

    // Basic validation of the response structure
    if (!response || !Array.isArray(response.slots)) {
      console.warn(
        `Received unexpected response format for available slots:`,
        response
      );
      // Return a default empty state or throw a more specific error
      return { slots: [] };
    }

    return response;
  } catch (error) {
    console.error('Error fetching available slots:', error);

    // Handle specific error types or messages if possible
    if (error && typeof error === 'object' && 'message' in error) {
      throw new Error(
        `Failed to fetch available slots: ${error.message as string}`
      );
    }

    throw new Error(
      'Failed to fetch available slots due to an unexpected error.'
    );
  }
}
