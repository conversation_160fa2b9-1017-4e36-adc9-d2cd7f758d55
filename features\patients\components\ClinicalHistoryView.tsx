'use client';

import { useState } from 'react';
import { ClinicalHistory } from '../types';
import { getMockClinicalHistory } from '../helpers/mock-clinical-history';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import {
  FileText,
  Activity,
  Stethoscope,
  Pill,
  AlertCircle,
  Calendar,
  ClipboardList,
  Thermometer,
  X
} from 'lucide-react';

interface ClinicalHistoryViewProps {
  patientId: string;
  isOpen: boolean;
  onClose: () => void;
}

export function ClinicalHistoryView({
  patientId,
  isOpen,
  onClose
}: ClinicalHistoryViewProps) {
  const [clinicalHistory, setClinicalHistory] = useState<ClinicalHistory>(
    getMockClinicalHistory(patientId)
  );

  // Helper function to format dates
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'dd/MM/yyyy', { locale: es });
    } catch (error) {
      return dateString;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] max-w-[90vw] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between text-xl font-semibold">
            <span>Historia Clínica del Paciente</span>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-4 md:grid-cols-8">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="appointments">Consultas</TabsTrigger>
            <TabsTrigger value="vitals">Signos Vitales</TabsTrigger>
            <TabsTrigger value="diagnoses">Diagnósticos</TabsTrigger>
            <TabsTrigger value="medications">Medicamentos</TabsTrigger>
            <TabsTrigger value="studies">Estudios</TabsTrigger>
            <TabsTrigger value="allergies">Alergias</TabsTrigger>
            <TabsTrigger value="history">Antecedentes</TabsTrigger>
          </TabsList>

          {/* General Tab */}
          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  Resumen General
                </CardTitle>
                <CardDescription>
                  Información general del historial clínico
                </CardDescription>
              </CardHeader>
              <CardContent className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="mb-2 font-semibold">Diagnósticos Activos</h3>
                  <ul className="list-inside list-disc space-y-1">
                    {clinicalHistory.diagnoses
                      .filter((d) => d.status === 'active')
                      .map((diagnosis) => (
                        <li key={diagnosis.id}>
                          {diagnosis.description}{' '}
                          {diagnosis.icdCode && `(${diagnosis.icdCode})`}
                        </li>
                      ))}
                  </ul>
                </div>

                <div>
                  <h3 className="mb-2 font-semibold">Medicamentos Actuales</h3>
                  <ul className="list-inside list-disc space-y-1">
                    {clinicalHistory.medications
                      .filter((m) => m.status === 'active')
                      .map((medication) => (
                        <li key={medication.id}>
                          {medication.name} - {medication.dosage},{' '}
                          {medication.frequency}
                        </li>
                      ))}
                  </ul>
                </div>

                <div>
                  <h3 className="mb-2 font-semibold">Alergias</h3>
                  <ul className="list-inside list-disc space-y-1">
                    {clinicalHistory.allergies.map((allergy) => (
                      <li key={allergy.id}>
                        {allergy.allergen} - {allergy.reaction} (
                        {allergy.severity})
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="mb-2 font-semibold">Última Consulta</h3>
                  {clinicalHistory.appointments.length > 0 && (
                    <div className="rounded-md border p-3">
                      <p>
                        <strong>Fecha:</strong>{' '}
                        {formatDate(clinicalHistory.appointments[0].date)}
                      </p>
                      <p>
                        <strong>Médico:</strong>{' '}
                        {clinicalHistory.appointments[0].doctorName}
                      </p>
                      <p>
                        <strong>Motivo:</strong>{' '}
                        {clinicalHistory.appointments[0].reason}
                      </p>
                      {clinicalHistory.appointments[0].notes && (
                        <p>
                          <strong>Notas:</strong>{' '}
                          {clinicalHistory.appointments[0].notes}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Appointments Tab */}
          <TabsContent value="appointments">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="mr-2 h-5 w-5" />
                  Historial de Consultas
                </CardTitle>
                <CardDescription>
                  Registro de todas las consultas del paciente
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Fecha</TableHead>
                      <TableHead>Hora</TableHead>
                      <TableHead>Médico</TableHead>
                      <TableHead>Motivo</TableHead>
                      <TableHead>Estado</TableHead>
                      <TableHead>Notas</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clinicalHistory.appointments.map((appointment) => (
                      <TableRow key={appointment.id}>
                        <TableCell>{formatDate(appointment.date)}</TableCell>
                        <TableCell>
                          {appointment.startTime} - {appointment.endTime}
                        </TableCell>
                        <TableCell>{appointment.doctorName}</TableCell>
                        <TableCell>{appointment.reason}</TableCell>
                        <TableCell>
                          <span
                            className={`rounded-full px-2 py-1 text-xs font-medium ${
                              appointment.status === 'completed'
                                ? 'bg-green-100 text-green-800'
                                : appointment.status === 'scheduled'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {appointment.status === 'completed'
                              ? 'Completada'
                              : appointment.status === 'scheduled'
                              ? 'Programada'
                              : 'Cancelada'}
                          </span>
                        </TableCell>
                        <TableCell>{appointment.notes || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Vital Signs Tab */}
          <TabsContent value="vitals">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="mr-2 h-5 w-5" />
                  Signos Vitales
                </CardTitle>
                <CardDescription>
                  Registro de signos vitales del paciente
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Fecha</TableHead>
                      <TableHead>Temperatura</TableHead>
                      <TableHead>Presión Arterial</TableHead>
                      <TableHead>Frecuencia Cardíaca</TableHead>
                      <TableHead>Peso</TableHead>
                      <TableHead>Altura</TableHead>
                      <TableHead>IMC</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clinicalHistory.vitalSigns.map((vitalSign) => (
                      <TableRow key={vitalSign.id}>
                        <TableCell>{formatDate(vitalSign.date)}</TableCell>
                        <TableCell>
                          {vitalSign.temperature
                            ? `${vitalSign.temperature}°C`
                            : '-'}
                        </TableCell>
                        <TableCell>
                          {vitalSign.bloodPressureSystolic &&
                          vitalSign.bloodPressureDiastolic
                            ? `${vitalSign.bloodPressureSystolic}/${vitalSign.bloodPressureDiastolic} mmHg`
                            : '-'}
                        </TableCell>
                        <TableCell>
                          {vitalSign.heartRate
                            ? `${vitalSign.heartRate} lpm`
                            : '-'}
                        </TableCell>
                        <TableCell>
                          {vitalSign.weight ? `${vitalSign.weight} kg` : '-'}
                        </TableCell>
                        <TableCell>
                          {vitalSign.height ? `${vitalSign.height} cm` : '-'}
                        </TableCell>
                        <TableCell>
                          {vitalSign.bmi ? vitalSign.bmi.toFixed(1) : '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Diagnoses Tab */}
          <TabsContent value="diagnoses">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Stethoscope className="mr-2 h-5 w-5" />
                  Diagnósticos
                </CardTitle>
                <CardDescription>
                  Diagnósticos médicos del paciente
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Fecha</TableHead>
                      <TableHead>Descripción</TableHead>
                      <TableHead>Código CIE</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Estado</TableHead>
                      <TableHead>Notas</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clinicalHistory.diagnoses.map((diagnosis) => (
                      <TableRow key={diagnosis.id}>
                        <TableCell>{formatDate(diagnosis.date)}</TableCell>
                        <TableCell>{diagnosis.description}</TableCell>
                        <TableCell>{diagnosis.icdCode || '-'}</TableCell>
                        <TableCell>
                          {diagnosis.type === 'primary'
                            ? 'Primario'
                            : 'Secundario'}
                        </TableCell>
                        <TableCell>
                          <span
                            className={`rounded-full px-2 py-1 text-xs font-medium ${
                              diagnosis.status === 'active'
                                ? 'bg-green-100 text-green-800'
                                : diagnosis.status === 'resolved'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}
                          >
                            {diagnosis.status === 'active'
                              ? 'Activo'
                              : diagnosis.status === 'resolved'
                              ? 'Resuelto'
                              : 'Recurrente'}
                          </span>
                        </TableCell>
                        <TableCell>{diagnosis.notes || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Medications Tab */}
          <TabsContent value="medications">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Pill className="mr-2 h-5 w-5" />
                  Medicamentos
                </CardTitle>
                <CardDescription>
                  Medicamentos prescritos al paciente
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Medicamento</TableHead>
                      <TableHead>Dosis</TableHead>
                      <TableHead>Vía</TableHead>
                      <TableHead>Frecuencia</TableHead>
                      <TableHead>Inicio</TableHead>
                      <TableHead>Estado</TableHead>
                      <TableHead>Prescrito por</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clinicalHistory.medications.map((medication) => (
                      <TableRow key={medication.id}>
                        <TableCell>{medication.name}</TableCell>
                        <TableCell>{medication.dosage}</TableCell>
                        <TableCell>{medication.route}</TableCell>
                        <TableCell>{medication.frequency}</TableCell>
                        <TableCell>
                          {formatDate(medication.startDate)}
                        </TableCell>
                        <TableCell>
                          <span
                            className={`rounded-full px-2 py-1 text-xs font-medium ${
                              medication.status === 'active'
                                ? 'bg-green-100 text-green-800'
                                : medication.status === 'discontinued'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-blue-100 text-blue-800'
                            }`}
                          >
                            {medication.status === 'active'
                              ? 'Activo'
                              : medication.status === 'discontinued'
                              ? 'Discontinuado'
                              : 'Completado'}
                          </span>
                        </TableCell>
                        <TableCell>{medication.prescribedBy}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Studies Tab */}
          <TabsContent value="studies">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <ClipboardList className="mr-2 h-5 w-5" />
                  Estudios
                </CardTitle>
                <CardDescription>
                  Estudios y análisis realizados
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Fecha</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Descripción</TableHead>
                      <TableHead>Solicitado por</TableHead>
                      <TableHead>Resultados</TableHead>
                      <TableHead>Archivos</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clinicalHistory.studies.map((study) => (
                      <TableRow key={study.id}>
                        <TableCell>{formatDate(study.date)}</TableCell>
                        <TableCell>{study.type}</TableCell>
                        <TableCell>{study.description}</TableCell>
                        <TableCell>{study.requestedBy}</TableCell>
                        <TableCell>{study.results || '-'}</TableCell>
                        <TableCell>
                          {study.fileUrls && study.fileUrls.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {study.fileUrls.map((url, index) => (
                                <Button
                                  key={index}
                                  variant="outline"
                                  size="sm"
                                  className="h-7 text-xs"
                                  onClick={() => window.open(url, '_blank')}
                                >
                                  Ver archivo {index + 1}
                                </Button>
                              ))}
                            </div>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Allergies Tab */}
          <TabsContent value="allergies">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertCircle className="mr-2 h-5 w-5" />
                  Alergias
                </CardTitle>
                <CardDescription>
                  Registro de alergias del paciente
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Alérgeno</TableHead>
                      <TableHead>Reacción</TableHead>
                      <TableHead>Severidad</TableHead>
                      <TableHead>Fecha Identificada</TableHead>
                      <TableHead>Estado</TableHead>
                      <TableHead>Notas</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clinicalHistory.allergies.map((allergy) => (
                      <TableRow key={allergy.id}>
                        <TableCell>{allergy.allergen}</TableCell>
                        <TableCell>{allergy.reaction}</TableCell>
                        <TableCell>
                          <span
                            className={`rounded-full px-2 py-1 text-xs font-medium ${
                              allergy.severity === 'mild'
                                ? 'bg-green-100 text-green-800'
                                : allergy.severity === 'moderate'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {allergy.severity === 'mild'
                              ? 'Leve'
                              : allergy.severity === 'moderate'
                              ? 'Moderada'
                              : 'Severa'}
                          </span>
                        </TableCell>
                        <TableCell>
                          {formatDate(allergy.dateIdentified)}
                        </TableCell>
                        <TableCell>
                          {allergy.status === 'active' ? 'Activa' : 'Inactiva'}
                        </TableCell>
                        <TableCell>{allergy.notes || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Thermometer className="mr-2 h-5 w-5" />
                  Antecedentes
                </CardTitle>
                <CardDescription>
                  Antecedentes médicos, familiares y personales
                </CardDescription>
              </CardHeader>
              <CardContent className="grid gap-6">
                <div>
                  <h3 className="mb-2 text-lg font-semibold">
                    Antecedentes Médicos
                  </h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Condición</TableHead>
                        <TableHead>Fecha Diagnóstico</TableHead>
                        <TableHead>Fecha Resolución</TableHead>
                        <TableHead>Estado</TableHead>
                        <TableHead>Notas</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {clinicalHistory.pastMedicalHistory.map((history) => (
                        <TableRow key={history.id}>
                          <TableCell>{history.condition}</TableCell>
                          <TableCell>
                            {history.diagnosisDate
                              ? formatDate(history.diagnosisDate)
                              : '-'}
                          </TableCell>
                          <TableCell>
                            {history.resolutionDate
                              ? formatDate(history.resolutionDate)
                              : '-'}
                          </TableCell>
                          <TableCell>
                            {history.status === 'resolved'
                              ? 'Resuelto'
                              : 'En curso'}
                          </TableCell>
                          <TableCell>{history.notes || '-'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div>
                  <h3 className="mb-2 text-lg font-semibold">
                    Antecedentes Familiares
                  </h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Condición</TableHead>
                        <TableHead>Parentesco</TableHead>
                        <TableHead>Notas</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {clinicalHistory.familyHistory.map((history) => (
                        <TableRow key={history.id}>
                          <TableCell>{history.condition}</TableCell>
                          <TableCell>{history.relationship}</TableCell>
                          <TableCell>{history.notes || '-'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div>
                  <h3 className="mb-2 text-lg font-semibold">
                    Antecedentes Quirúrgicos
                  </h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Procedimiento</TableHead>
                        <TableHead>Fecha</TableHead>
                        <TableHead>Cirujano</TableHead>
                        <TableHead>Hospital</TableHead>
                        <TableHead>Notas</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {clinicalHistory.surgicalHistory.map((history) => (
                        <TableRow key={history.id}>
                          <TableCell>{history.procedure}</TableCell>
                          <TableCell>{formatDate(history.date)}</TableCell>
                          <TableCell>{history.surgeon || '-'}</TableCell>
                          <TableCell>{history.hospital || '-'}</TableCell>
                          <TableCell>{history.notes || '-'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button onClick={onClose}>Cerrar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
