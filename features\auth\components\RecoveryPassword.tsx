'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { PATHROUTES } from '@/helpers';
import Link from 'next/link';
import { RecoverySchema, RecoverySchemaType } from '../lib/zod/schema';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import { api } from '../lib/axios';

const RecoveryPassword = () => {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<RecoverySchemaType>({
    resolver: zodResolver(RecoverySchema),
    defaultValues: {
      password: '',
      confirmPassword: ''
    }
  });

  const onSubmit = async (data: RecoverySchemaType) => {
    if (!token) {
      toast.error('Token de recuperación no válido');
      return;
    }

    setLoading(true);
    try {
      await api.post('/auth/reset-password', {
        password: data.password,
        token
      });

      toast.success('Contraseña actualizada correctamente');
      reset();

      // Redirigir al login después de un breve retraso
      setTimeout(() => {
        router.push(PATHROUTES.LOGIN);
      }, 2000);
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || 'Error al actualizar la contraseña'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-[500px] space-y-5 border-2 bg-white px-5 py-3 shadow-lg">
      <p className="text-center text-[28px] font-bold text-bluePrimary">
        Cambiar contraseña
      </p>
      <p className="text-center text-sm">
        Ingresa y confirma tu nueva contraseña
      </p>

      <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-col">
          <label htmlFor="password">Contraseña</label>
          <input
            className="border-red border-2 border-slate-300 bg-inputbg p-2 "
            type="password"
            placeholder="Ingrese su nueva contraseña"
            {...register('password')}
          />
          {errors.password && (
            <p className="text-xs text-red-500">{errors.password.message}</p>
          )}
        </div>

        <div className="flex flex-col">
          <label htmlFor="confirmPassword">Confirma Contraseña</label>
          <input
            className="border-red border-2 border-slate-300 bg-inputbg p-2 "
            type="password"
            placeholder="Ingrese su nueva contraseña"
            {...register('confirmPassword')}
          />
          {errors.confirmPassword && (
            <p className="text-xs text-red-500">
              {errors.confirmPassword.message}
            </p>
          )}
        </div>

        <button
          className="flex w-full justify-center rounded-xl bg-bluePrimary py-2 font-bold text-white"
          type="submit"
          disabled={loading}
        >
          {loading ? 'Actualizando...' : 'Cambiar contraseña'}
        </button>
      </form>
      <p className="text-center">
        ¿Necesitas iniciar sesión?{' '}
        <Link href={PATHROUTES.LOGIN}>
          <span className="text-bluePrimary">Ingresa aca</span>
        </Link>
      </p>
    </div>
  );
};

export default RecoveryPassword;
