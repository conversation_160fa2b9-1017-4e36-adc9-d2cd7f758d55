import { useEffect, useCallback } from 'react';
import { socket, connectWithAuth } from '@/lib/socketio';
import { useAlarmasChatStore } from '@/features/alarmas/store/alarmasChat.store';
import { Message } from '@/features/alarmas/types/chatbot-alarmas';

export const useAlarmasChatHandlers = (userId: string) => {
  const {
    addMessage,
    setLoading,
    setError,
    setSessionId,
    setSessionStatus,
    setConnectionStatus,
    clearError
  } = useAlarmasChatStore();

  const sendMessage = useCallback(
    (message: string) => {
      if (!message.trim()) return;

      const userMessage: Message = {
        sender: 'user',
        message: message.trim(),
        timestamp: new Date()
      };

      addMessage(userMessage);
      setLoading(true);
      clearError();

      socket.emit(
        'alarmas:message',
        {
          userId,
          message: userMessage.message
        },
        (error: any) => {
          if (error) {
            if (
              error.message === 'Invalid token' ||
              error.message === 'Unauthorized connection'
            ) {
              setError(
                'Error de autenticación. Por favor, inicie sesión nuevamente.'
              );
            } else {
              setError(
                'Error al enviar el mensaje. Por favor, intente nuevamente.'
              );
            }
            setLoading(false);
          }
        }
      );
    },
    [userId, addMessage, setLoading, setError, clearError]
  );

  const handleConnectionError = useCallback(() => {
    setConnectionStatus('disconnected');
    setError('Se perdió la conexión con el servidor. Intentando reconectar...');
  }, [setConnectionStatus, setError]);

  const handleReconnectAttempt = useCallback(() => {
    setConnectionStatus('reconnecting');
  }, [setConnectionStatus]);

  const handleReconnect = useCallback(() => {
    setConnectionStatus('connected');
    clearError();
    socket.emit('alarmas:join', { userId });
  }, [userId, setConnectionStatus, clearError]);

  useEffect(() => {
    setConnectionStatus('disconnected');
    console.log('Connecting to chat service...');

    // Intentar conectar con autenticación
    const connected = connectWithAuth();
    if (!connected) {
      setError('No se pudo establecer la conexión: token no disponible');
      return;
    }

    socket.emit('alarmas:join', { userId }, (error: any) => {
      if (error) {
        if (
          error.message === 'Invalid token' ||
          error.message === 'Unauthorized connection'
        ) {
          setError(
            'Error de autenticación. Por favor, inicie sesión nuevamente.'
          );
        } else {
          setError('Error al conectar con el servicio de chat.');
        }
        setConnectionStatus('disconnected');
        return;
      }
      console.log('Connected to chat service');
      setConnectionStatus('connected');
      setSessionStatus('active');
    });

    // Manejadores de respuestas del bot
    const handleBotResponse = (data: {
      message: string;
      sessionId?: string;
    }) => {
      setLoading(false);
      if (data.sessionId) {
        setSessionId(data.sessionId);
      }
      addMessage({
        sender: 'bot',
        message: data.message,
        timestamp: new Date()
      });
    };

    // Manejadores de errores y estado de sesión
    const handleError = (error: string) => {
      setLoading(false);
      if (error === 'Invalid token' || error === 'Unauthorized connection') {
        setError(
          'Error de autenticación. Por favor, inicie sesión nuevamente.'
        );
      } else {
        setError(error);
      }
    };

    const handleSessionEnd = (data: { status: 'completed' | 'cancelled' }) => {
      setSessionStatus('ended');
      addMessage({
        sender: 'bot',
        message:
          data.status === 'completed'
            ? 'Chat finalizado. ¡Gracias por usar nuestro servicio!'
            : 'Chat cancelado.',
        timestamp: new Date()
      });
    };

    // Suscripción a eventos
    socket.on('connect', () => setConnectionStatus('connected'));
    socket.on('disconnect', handleConnectionError);
    socket.on('reconnect_attempt', handleReconnectAttempt);
    socket.on('reconnect', handleReconnect);
    socket.on('alarmas:bot_response', handleBotResponse);
    socket.on('alarmas:error', handleError);
    socket.on('alarmas:session_end', handleSessionEnd);

    return () => {
      socket.off('connect');
      socket.off('disconnect', handleConnectionError);
      socket.off('reconnect_attempt', handleReconnectAttempt);
      socket.off('reconnect', handleReconnect);
      socket.off('alarmas:bot_response', handleBotResponse);
      socket.off('alarmas:error', handleError);
      socket.off('alarmas:session_end', handleSessionEnd);
      socket.emit('alarmas:leave', { userId });
      socket.disconnect();
    };
  }, [
    userId,
    addMessage,
    setLoading,
    setError,
    setSessionId,
    setSessionStatus,
    setConnectionStatus,
    handleConnectionError,
    handleReconnectAttempt,
    handleReconnect,
    clearError
  ]);

  return { sendMessage };
};
