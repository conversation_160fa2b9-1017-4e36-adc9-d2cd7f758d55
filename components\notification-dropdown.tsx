'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import {
  Loader2,
  Check,
  Info,
  AlertTriangle,
  CheckCircle2,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useNotifications } from '@/hooks/use-notifications';
import type { Notification } from '@/lib/mock-notifications';
import { Icons } from './icons';

export function NotificationDropdown() {
  const { notifications, isLoading, error, markAsRead, markAllAsRead } =
    useNotifications();

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative h-11 w-11 border-2 border-gray-200 text-gray-500 hover:text-gray-900"
        >
          <Bell className="h-[26px] w-[26px]" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center p-0"
            >
              {unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[500px]">
        <div className="flex items-center justify-between p-4">
          <div>
            <h3 className="font-semibold">Notificaciones</h3>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 text-sm font-normal"
                onClick={markAllAsRead}
              >
                <Check className="mr-1 h-3 w-3" />
                Marcar todas como leídas
              </Button>
            )}
          </div>
          <Button variant="ghost" size="sm">
            <Icons.close className="h-6 w-6" />
          </Button>
        </div>
        {isLoading ? (
          <div className="flex justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : error ? (
          <div className="p-4 text-sm text-destructive">{error}</div>
        ) : notifications.length === 0 ? (
          <div className="p-8 text-center text-sm text-muted-foreground">
            No notifications
          </div>
        ) : (
          <ScrollArea className="h-[300px]">
            <div className="flex flex-col">
              {notifications.map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={markAsRead}
                />
              ))}
            </div>
          </ScrollArea>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

const notificationIcons = {
  info: Info,
  warning: AlertTriangle,
  success: CheckCircle2,
  error: AlertCircle,
  patient: User,
  system: Settings
};

const notificationStyles = {
  info: 'text-white bg-blue-500',
  warning: 'text-yellow-500 bg-yellow-50',
  success: 'text-green-500 bg-green-50',
  error: 'text-red-500 bg-red-50',
  patient: 'text-white bg-blue-500',
  system: 'text-white bg-stone-500'
};

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
}

function NotificationItem({
  notification,
  onMarkAsRead
}: NotificationItemProps) {
  const Icon = notificationIcons[notification.type];

  return (
    <div
      className={cn(
        'flex cursor-pointer items-center gap-3 border-b px-3 py-2 transition-colors hover:bg-accent'
      )}
      onClick={() => !notification.isRead && onMarkAsRead(notification.id)}
    >
      <div
        className={cn(
          'rounded-full p-2',
          notificationStyles[notification.type]
        )}
      >
        <Icon className="h-5 w-5" />
      </div>
      <div className="flex-1 space-y-1">
        <p
          className={cn(
            'py-1 text-sm font-medium',
            !notification.isRead && 'font-semibold'
          )}
        >
          {notification.description}
        </p>
        <p className="text-xs text-muted-foreground">{notification.time}</p>
      </div>
      {!notification.isRead && (
        <div className="h-2 w-2 rounded-full bg-blue-500" />
      )}
    </div>
  );
}
