import axios from 'axios';

// Nota: Este archivo se mantiene para compatibilidad con código existente
// Se recomienda migrar a lib/api.ts para nuevas funcionalidades

export const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor to add authentication token
api.interceptors.request.use(
  (config) => {
    // For server components, we can't access localStorage directly
    // In a real app, you would use cookies or another server-side storage
    // This is a simplified example
    let token = '';

    // In browser context
    if (typeof window !== 'undefined') {
      token = localStorage.getItem('token') || '';
    }

    // Add token to headers if available
    if (token) {
      // Ensure headers object exists
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const errorMessage = error.response?.data?.message || 'Error in request';

    // Handle token expiration
    if (error.response?.status === 401) {
      // Para código del lado del cliente, redirigir al login
      if (typeof window !== 'undefined') {
        // Redirigir a la página de login
        window.location.href = '/auth/login';
      }
    }

    return Promise.reject({
      message: errorMessage,
      status: error.response?.status || 500
    });
  }
);
