'use client';
import React from 'react';

interface BodyViewProps {
  selectedParts: string[];
  onPartClick: (part: string) => void;
}

const BodyView: React.FC<BodyViewProps> = ({ selectedParts, onPartClick }) => {
  const isSelected = (part: string) => selectedParts.includes(part);

  return (
    <div>
      <svg
        version="1.1"
        id="Layer_1"
        x="0px"
        y="0px"
        viewBox="0 0 1243.8 1340.1"
        xmlSpace="preserve"
        fill="white"
        stroke="gray"
        strokeWidth="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        className="w-[32rem] stroke-current transition-colors duration-300"
      >
        {/* Cuello zona Frontal */}
        <g
          data-part="3"
          className={`stroke-gray-700 ${
            isSelected('3') ? 'fill-[#ffb6dd]' : 'hover:fill-[#ffb6dd]'
          }`}
          onClick={() => onPartClick('3')}
        >
          <path d="M242.4,243.2c0.6,0,5-2.9,5-2.9c8.2-5.3,14.3-12.4,16.6-22.5c0.4-1.9,1.1-4.7,1-6.2l0.3-0.6   c4.7,4.5,11.3,7.5,17.7,9.7c6.4,2.3,12.8,5,20,3.6c10-1.9,18.9-6.2,26.9-12.1c0.9-0.6,3.7-3.1,6.4-5.5l0.5,0.5l-0.5,3.7   c-0.6,3.2,0.1,6.5,2.2,11c4.6,10.3,12.7,16.9,22.5,22"></path>
          <path d="M361.4,244.1c-9.8-5.1-17.9-11.7-22.5-22c-2-4.5-2.8-7.8-2.2-11l0.5-3.7"></path>
          <path d="M265.4,211.9c0.1,1.5-0.6,4.3-1,6.2c-2.4,10.1-8.5,17.2-16.6,22.5c0,0-4.4,2.9-5,2.9"></path>
        </g>

        {/* Cuello zona trasera */}
        <g
          data-part="4"
          className={`stroke-gray-700 ${
            isSelected('4') ? 'fill-[#ffb6dd]' : 'hover:fill-[#ffb6dd]'
          }`}
          onClick={() => onPartClick('4')}
        >
          <path d="M1020.3,245.1c-5.3-2.5-10.3-5.5-15-9.3c-5.8-4.8-9.6-10.7-11.1-17.8c-0.5-2.3,0.1-5.7,0.6-7.6l-71.1-0.7   c2.1,6.4,0.1,12.9-5.4,19.2c-5.4,6.2-11.8,10.8-18.8,14.5"></path>
          <path d="M993.7,211.6c-0.6,2-0.7,4.1-0.2,6.4c1.5,7.2,5.3,13.2,10.9,18c4.5,3.9,9.5,6.9,14.7,9.4"></path>
          <path d="M924.2,209.6c2.1,6.5,0.1,13.1-5.3,19.4c-5.3,6.3-11.6,11-18.5,14.7"></path>
        </g>

        {/* Pierna derecha frontal */}
        <g
          data-part="11"
          className={`stroke-gray-700 ${
            isSelected('11') ? 'fill-[#9be5d8]' : 'hover:fill-[#9be5d8]'
          }`}
          onClick={() => onPartClick('11')}
        >
          <path d="M421.5,650c1.1,10.8,2.4,21.6,1.8,32.5c-0.6,11.9-1.4,23.7-2.7,35.5c-0.8,7.1-1.4,14.3-2.4,21.5  c-1.4,10.7-2.8,21.4-4.6,32.1c-1.8,11-3.4,22-5.3,33c-4.8,27.4-9.7,54.8-15.8,82c-2.6,11.6-4.7,23.3-7.1,35  c-2.1,10.5-0.7,21,0.6,31.5c0.1,0.6,0.4,1.3,0.4,2c0.3,8.7,1.2,17.4,0.9,26c-0.5,12.4-1.9,24.7-3.7,37c-4.2,27.5-11.8,54.2-19,81  c-3.2,11.9-6.5,23.7-8.1,36c-0.8,6.3-0.2,12.8,1.1,19c3.3,16.1,7.7,32,13.4,47.5c3.1,8.6,6.4,17.1,10,25.5c2.4,5.5,4,11.3,5.3,17  c1.4,6.4-1.4,11-6.9,12.5l-3-10.5c0.6,3.8,1.6,7.4,2.9,11c0.9,2.6-3.6,6.6-7.4,6.9c-3.7,0.3-5-11-5.5-14l3.1,13.8  c-3.5,8.2-11.6,3.6-11.6,3.6l-2.6-17.9c-0.1,6,1.3,11.7,2.6,17.9c-1.7,1.3-2.8,3.1-5.6,3.7c-5.7,1.2-7.5,0.2-8-5.6  c-0.4-4.3-1-8.7-1.5-13l2,17.5c-2,1-3.3,3-6,3.1c-5.7,0.3-9.5-2.3-11.7-10.6c-1-3.9-0.1-7.8-1.4-11.5c-3.2-9.9-3.8-20.1-3.5-30.5  c0.3-15.8-3-31-7.9-46c-2.9-8.9-4.8-18.2-2-27.5c3.2-10.5,5.6-20.7,4.2-32.1c-1.4-11.4-1.4-23-2.2-34.5c-0.7-10.3-1.6-20.7-2-31  c-0.3-9.5-1.9-19-1.2-28.5c0.1-0.8,0.1-1.7,0-2.5c-2-18.2-1.9-36.5,1.3-54.5c2.4-13.8,2.7-27.7,3.5-41.6c0.5-9.6,2-19.2,0.5-29  c-1.5-9-1.1-18.3-2.3-27.5c-1.3-10.3-2.2-20.6-2.9-31c-0.6-9.3-1.1-18.7-2.2-28c-1.4-12.5-1.8-25-2.6-37.5  c-0.8-13.8-0.2-27.7-0.2-41.5c0-1.8-0.3-3.7-0.5-5.5"></path>
        </g>

        {/* Pierna Izquierda frontal */}
        <g
          data-part="13"
          className={`stroke-gray-700 ${
            isSelected('13') ? 'fill-[#9be5d8]' : 'hover:fill-[#9be5d8]'
          }`}
          onClick={() => onPartClick('13')}
        >
          <path d="M180.3,658.5c-0.5,8.3-1.1,16.7-0.7,25c0.6,11.9,1.1,23.7,2.8,35.5c1.4,9.8,1.9,19.7,3.1,29.5  c1.1,9.4,2.9,18.6,4.1,28c1.1,9.4,3.2,18.7,4.9,28c3,17.2,5.8,34.4,9.5,51.5c3.7,17.3,6.7,34.7,10.7,52c3.1,13.8,4,27.6,2.8,41.5  c-0.9,10.8-2,21.6-1.9,32.5c0,2,0.3,4,0.5,6l0,2c0.8,7.7,1.5,15.3,2.4,23c0.9,8.1,2.6,16,4.2,24c5.6,27.5,13.8,54.4,20.8,81.6  c2.3,9,4.2,18.5,3.1,27.9c-1.3,10.9-3.8,21.6-7,32c-5.5,17.8-11.7,35.4-19.2,52.6c-2.7,6.2-5.3,13-2.9,20.4c1.1,3.4,2.8,4.9,6.1,5  l2.5-11c0.5,3.2-0.6,6.3-1.7,8.9c-2.3,5.6,3.1,10.8,9.2,9.1l3-13.5c-1,4.3-1.8,8.7-2.7,13.2c2.9,3.1,5.8,6.1,10.7,4.3l3-18  c-0.9,6-2.2,11.7-2.6,18c2.8,2.4,5.4,5,10.1,3.7c2.6-0.7,3.4-1.6,3.5-4.1c0.2-4.3,0.6-8.7,1-13l-1.5,16c2,1,3.3,3,6,3.1  c6.3,0.1,10.9-4.1,11.5-11.1c0.4-3.7,0.6-7.5,1.6-11c2.8-10,3.8-20.2,3.5-30.5c-0.5-14.8,2.8-29,6.9-43c2.9-9.8,5.9-19.6,3-30  c-1.3-4.7-2.8-9.3-3.9-14c-1.6-6.8-0.7-13.6-0.2-20.5c0.7-10.7,1.3-21.3,2-32c0.7-10.8,1.3-21.7,2.1-32.5c0.9-13.6,1.1-27.3,2-41  c0.8-12.8,0.1-25.7-1.6-38.5c-1.7-12.9-4-25.8-3.3-39c0-0.7,0-1.3,0-2c-0.7-11.3-2.5-22.5-1.2-34c1.6-14.3,3.1-28.6,4.1-43  c1.2-16,2.5-32,4-48c1.3-13.8,1.7-27.6,2.1-41.5c0.3-11.2,0-22.3,0.1-33.5c0-1.8,0.3-3.7,0.5-5.5"></path>
        </g>

        {/* Pecho zona frontal */}
        <g
          data-part="9"
          className={`stroke-gray-700 ${
            isSelected('9') ? 'fill-[#a67dc6]' : 'hover:fill-[#a67dc6]'
          }`}
          onClick={() => onPartClick('9')}
        >
          <path d="M185.2,342c0.4,6.9,3.1,13.3,4.2,20c2.5,14.4,6.2,28.5,8.2,43c0.8,6.2,2.3,12.2,2,18.5l0.4,6  c2.9,2.3,3,5.9,3.4,9c0.9,7.8,1.7,15.6,2.4,23.5c0.6,6.5,0.9,13,0.7,19.5c-0.4,12.3-2.6,24.3-6.8,36c-5.9,16.3-9.9,32.9-9.1,50.5  c0.6,12.7-0.4,25.4-2.1,38c-1.1,8.6-3.9,17-4.7,25.5c-0.8,8.4-2.7,19.1-3.4,27.6l241.3-9.7c-1.3-10.7-3.9-23.7-5.5-34.4  c-1.6-10.5-2.9-20.9-3.5-31.5c-0.6-10,0.5-20-0.9-30c-1.3-9.3-2.3-18.6-5.7-27.5c-6.7-17.7-10.7-35.9-9.5-55c0.6-9,0.8-18.1,2.2-27  c0.8-4.9-0.1-10.6,4.3-14.5l0.2-6c-0.3-6.3,1.2-12.4,2-18.5c2-14.5,5.7-28.6,8.2-43c1.2-6.7,3.8-13.1,4.2-20l-56.4-98.1l-118.9,0.9  L185.2,342z"></path>
        </g>

        {/* Espalda */}
        <g
          data-part="10"
          className={`stroke-gray-700 ${
            isSelected('10') ? 'fill-[#a67dc6]' : 'hover:fill-[#a67dc6]'
          }`}
          onClick={() => onPartClick('10')}
        >
          <path d="M899.5,244.2l120.3,1.7l47.4,80.5c6.8,11.5,9,25.1,6.7,38.2c0,0.1,0,0.1,0,0.2c-2.5,14.4-6.2,28.5-8.2,43  c-0.8,6.2-2.3,12.2-2,18.5l-1.9,9.4c-1.2,7.3-1.7,14.6-2.1,22c-0.2,3.5-1,6.9-1.2,10.5c-1.1,19.6,2.7,38.3,9.2,56.5  c4.7,13.2,6.8,26.7,6.9,40.5c0.1,11-0.5,22.1,1.1,33c1.2,8.7,3.2,17.3,4.4,26c1.1,8.1,2.6,16.2,3.6,24.4L835.2,654  c1.1-12,3.9-23.6,5.4-35.5c1.3-9.7,3.1-19.3,3.7-29c0.6-9.5,0.1-19,0.8-28.5c1-14,3.4-27.4,8.2-40.5c4.8-13.2,7.8-26.9,8-41  c0.2-14-1.7-27.7-3.8-41.5c-0.6-3.6,0.3-6.7-2.8-8.7l0.8-4.7c0.3-6.3-1.2-12.4-2-18.5c-2-14.5-5.7-28.6-8.2-43c0-0.1,0-0.1,0-0.2  c-2.3-13.2-0.1-26.7,6.7-38.2L899.5,244.2z"></path>
        </g>

        {/* Pierna trasera derecha */}
        <g
          data-part="14"
          className={`stroke-gray-700 ${
            isSelected('14') ? 'fill-[#9be5d8]' : 'hover:fill-[#9be5d8]'
          }`}
          onClick={() => onPartClick('14')}
        >
          <path d="M1083.8,650c1.1,10.8,2.4,21.6,1.8,32.5c-0.6,11.9-1.4,23.7-2.7,35.5c-0.8,7.1-1.4,14.3-2.4,21.5  c-1.4,10.7-2.8,21.4-4.6,32.1c-1.8,11-3.4,22-5.3,33c-4.8,27.4-9.7,54.8-15.8,82c-2.6,11.6-4.7,23.3-7.1,35  c-2.1,10.5-0.7,21,0.6,31.5c0.1,0.6,0.4,1.3,0.4,2c0.3,8.7,1.2,17.4,0.9,26c-0.5,12.4-1.9,24.7-3.7,37c-4.2,27.5-11.8,54.2-19,81  c-3.2,11.9-6.5,23.7-8.1,36c-0.8,6.3-0.2,12.8,1.1,19c3.3,16.1,7.7,32,13.4,47.5c3.1,8.6,6.4,17.1,10,25.5c2.4,5.5,4,11.3,5.3,17  c1.4,6.4-1.4,11-6.9,12.5l-3-10.5c0.6,3.8,1.6,7.4,2.9,11c0.9,2.6-3.6,6.6-7.4,6.9c-3.7,0.3-5-11-5.5-14l3.1,13.8  c-3.5,8.2-11.6,3.6-11.6,3.6l-2.6-17.9c-0.1,6,1.3,11.7,2.6,17.9c-1.7,1.3-2.8,3.1-5.6,3.7c-5.7,1.2-7.5,0.2-8-5.6  c-0.4-4.3-1-8.7-1.5-13l2,17.5c-2,1-3.3,3-6,3.1c-5.7,0.3-9.5-2.3-11.7-10.6c-1-3.9-0.1-7.8-1.4-11.5c-3.2-9.9-3.8-20.1-3.5-30.5  c0.3-15.8-3-31-7.9-46c-2.9-8.9-4.8-18.2-2-27.5c3.2-10.5,5.6-20.7,4.2-32.1c-1.4-11.4-1.4-23-2.2-34.5c-0.7-10.3-1.6-20.7-2-31  c-0.3-9.5-1.9-19-1.2-28.5c0.1-0.8,0.1-1.7,0-2.5c-2-18.2-1.9-36.5,1.3-54.5c2.4-13.8,2.7-27.7,3.5-41.6c0.5-9.6,2-19.2,0.5-29  c-1.5-9-1.1-18.3-2.3-27.5c-1.3-10.3-2.2-20.6-2.9-31c-0.6-9.3-1.1-18.7-2.2-28c-1.4-12.5-1.8-25-2.6-37.5  c-0.8-13.8-0.2-27.7-0.2-41.5"></path>
          <path d="M1031.9,932c-3.2-0.3-6.4-0.4-9.5-1.1c-8.1-1.8-16.1-0.8-24,1.1"></path>
          <path d="M1002.9,1155.5c0.1-1.5-0.3-2.5-2-3c-5.4-1.8-12.5-2-16,5"></path>
        </g>

        {/* Pierna trasera Izquierda */}
        <g
          data-part="12"
          className={`stroke-gray-700 ${
            isSelected('12') ? 'fill-[#9be5d8]' : 'hover:fill-[#9be5d8]'
          }`}
          onClick={() => onPartClick('12')}
        >
          <path d="M835.1,654.9c-0.5,8.3-1.3,20.3-0.9,28.7c0.6,11.9,1.1,23.7,2.8,35.5c1.4,9.8,1.9,19.7,3.1,29.5  c1.1,9.4,2.9,18.6,4.1,28c1.1,9.4,3.2,18.7,4.9,28c3,17.2,5.8,34.4,9.5,51.5c3.7,17.3,6.7,34.7,10.7,52c3.1,13.8,4,27.6,2.8,41.5  c-0.9,10.8-2,21.6-1.9,32.5c0,2,0.3,4,0.5,6l0,2c0.8,7.7,1.5,15.3,2.4,23c0.9,8.1,2.6,16,4.2,24c5.6,27.5,13.8,54.4,20.8,81.6  c2.3,9,4.2,18.5,3.1,27.9c-1.3,10.9-3.8,21.6-7,32c-5.5,17.8-11.7,35.4-19.2,52.6c-2.7,6.2-5.3,13-2.9,20.4c1.1,3.4,2.8,4.9,6.1,5  l2.5-11c0.5,3.2-0.6,6.3-1.7,8.9c-2.3,5.6,3.1,10.8,9.2,9.1l3-13.5c-1,4.3-1.8,8.7-2.7,13.2c2.9,3.1,5.8,6.1,10.7,4.3l3-18  c-0.9,6-2.2,11.7-2.6,18c2.8,2.4,5.4,5,10.1,3.7c2.6-0.7,3.4-1.6,3.5-4.1c0.2-4.3,0.6-8.7,1-13l-1.5,16c2,1,3.3,3,6,3.1  c6.3,0.1,10.9-4.1,11.5-11.1c0.4-3.7,0.6-7.5,1.6-11c2.8-10,3.8-20.2,3.5-30.5c-0.5-14.8,2.8-29,6.9-43c2.9-9.8,5.9-19.6,3-30  c-1.3-4.7-2.8-9.3-3.9-14c-1.6-6.8-0.7-13.6-0.2-20.5c0.7-10.7,1.3-21.3,2-32c0.7-10.8,1.3-21.7,2.1-32.5c0.9-13.6,1.1-27.3,2-41  c0.8-12.8,0.1-25.7-1.6-38.5c-1.7-12.9-4-25.8-3.3-39c0-0.7,0-1.3,0-2c-0.7-11.3-2.5-22.5-1.2-34c1.6-14.3,3.1-28.6,4.1-43  c1.2-16,2.5-32,4-48c1.3-13.8,1.7-27.6,2.1-41.5c0.3-11.2,0-22.3,0.1-33.5"></path>
          <path d="M922.9,932c-2.3,0.5-4.4-0.5-6.5-1c-9.1-2.2-18-0.3-27,1"></path>
          <path d="M933.9,1157.5c-2.7-5.1-6.3-7-12-6c-2.1,0.4-4,1.3-6,2"></path>
        </g>

        {/* Pelvis zona trasera */}
        <g
          data-part="16"
          className={`stroke-gray-700 ${
            isSelected('16') ? 'fill-[#ffd0c8]' : 'hover:fill-[#ffd0c8]'
          }`}
          onClick={() => onPartClick('16')}
        >
          <path d="m 837.25,734.81621 c -1.76,-10.36573 -2.74669,-22.5031 -3.35669,-32.91994 -0.42,-7.31649 -0.85056,-15.07559 -0.75842,-20.27152 l 0.37669,-13.79303 c 0.72,-7.4272 1.46913,-13.43724 1.98158,-19.1046 1.00684,-4.74167 1.60425,-9.51659 2.90489,-13.66937 l 242.79715,-0.0682 c 1.2243,8.56778 2.9089,20.05184 4.0349,31.79774 l 1.1917,11.43367 c -0.08,9.94238 -0.8452,15.16743 -1.4052,24.71548 -0.62,10.42535 -1.926,21.74678 -2.79,31.22495 0,0 -87.22156,26.09318 -113.83307,-2.78628 -8.94021,-11.96459 -12.53895,-6.01044 -18.88713,0.31515 -26.84721,26.56514 -112.2564,3.12592 -112.2564,3.12592 z"></path>
          <path d="M1043.9,730.5c-9.5,3-19.1,5.9-29,6.9c-14.5,1.6-28.7,1.3-41.6-7.3c-3.3-2.2-6.1-4.4-8.6-7.5   c-1.7-2.1-4.5-3.9-7.9-1.7c-4.4,2.9-5.8,4.6-6,9.3"></path>
          <path d="M876.9,731c9,2.7,18.1,5.5,27.5,6.4c11.1,1.1,22.2,1.4,33.1-2.8c4.6-1.8,7.7-6.4,12.9-6.7"></path>
          <path d="M959.7,694c0.4,8.5,0.3,17-0.2,25.5"></path>
        </g>

        {/* Detallitos esteticos */}
        <g>
          <path d="M402.9,429.5c-4.4,3.9-3.5,9.6-4.3,14.5c-1.4,8.9-1.6,18-2.2,27c-1.3,19.1,2.8,37.3,9.5,55  c3.3,8.9,4.4,18.1,5.7,27.5c1.4,10,0.3,20,0.9,30c0.6,10.6,1.8,21.1,3.5,31.5c1.7,10.6,4.2,23.7,5.5,34.4"></path>
        </g>
        <g>
          <path d="M1063.5,430c-1.2,1.7-1.4,3.9-1.7,5.7c-1.2,7.3-1.7,14.6-2.1,22c-0.2,3.5-1,6.9-1.2,10.5  c-1.1,19.6,2.7,38.3,9.2,56.5c4.7,13.2,6.8,26.7,6.9,40.5c0.1,11-0.5,22.1,1.1,33c1.2,8.7,3.2,17.3,4.4,26  c1.1,8.1,2.6,16.2,3.6,24.4"></path>
        </g>
        <g>
          <path d="M854.8,429.3c3.1,2,2.2,5.1,2.8,8.7c2.2,13.8,4,27.6,3.8,41.5c-0.2,14.1-3.2,27.8-8,41  c-4.8,13.1-7.2,26.6-8.2,40.5c-0.7,9.5-0.2,19-0.8,28.5c-0.6,9.8-2.5,19.4-3.7,29c-1.6,11.9-4.4,23.5-5.4,35.5"></path>
        </g>
        <g>
          <path d="M199.9,429.5c2.9,2.3,3,5.9,3.4,9c0.9,7.8,1.7,15.6,2.4,23.5c0.6,6.5,0.9,13,0.7,19.5   c-0.4,12.3-2.6,24.3-6.8,36c-5.9,16.3-9.9,32.9-9.1,50.5c0.6,12.7-0.4,25.4-2.1,38c-1.1,8.6-3.9,17-4.7,25.5   c-0.8,8.4-2.7,19.1-3.4,27.6"></path>
        </g>

        {/* Cabeza zona trasera */}
        <g
          data-part="2"
          className={`stroke-gray-700 ${
            isSelected('2') ? 'fill-[#C6F9FC]' : 'hover:fill-[#C6F9FC]'
          }`}
          onClick={() => onPartClick('2')}
        >
          <path d="M994,210.4c1.1-3.1,3.2-5.6,5.4-8.3c6.9-8.4,11-17.8,10-29l0.1,7.4c6.9,1.6,13-1.2,16.9-6.5  c6.6-9,6.5-19,2.9-28.9c-2.2-6.1-7.5-8.2-12.9-6l1-1.5c-1.7-2-1.3-4.4-0.9-6.5c2-9.6-0.1-19-2.8-28c-5.2-17.6-17.3-28.5-35.3-32.5  c-15.2-3.5-30.4-3.2-44.9,2.2c-17.8,6.5-26.6,20.8-30.1,38.8c-1.3,6.7-0.8,13.3-0.9,20c-0.1,2,0.2,4-0.6,6c-10.4,2.1-12.1,4-13.4,16  c-0.9,8.1,0,15.6,5.9,22.1c4.1,4.4,9.3,6.4,15.3,4.9l0.3-11c-1.9,12.7,2.8,23.3,10.4,33c1.5,1.9,3.1,5.1,3.9,7.1"></path>
        </g>

        {/* Pelvis zona frontal */}
        <g
          data-part="15"
          className={`stroke-gray-700 ${
            isSelected('15') ? 'fill-[#ffd0c8]' : 'hover:fill-[#ffd0c8]'
          }`}
          onClick={() => onPartClick('15')}
        >
          <path d="M359.9,671.5c-1.5-0.5-2.2,0.6-3,1.5c-11.4,13.5-23.3,26.6-36.5,38.5c-3.4,3.1-7,6.3-11,8.4   c-6.6,3.4-13.7,2.8-19.4-2c-16.7-14-32.1-29.5-45.3-46.9"></path>
        </g>

        {/* Cabeza zona Frontal */}
        <g
          data-part="1"
          className={`stroke-gray-700 ${
            isSelected('1') ? 'fill-[#C6F9FC]' : 'hover:fill-[#C6F9FC]'
          }`}
          onClick={() => onPartClick('1')}
        >
          <path d="M330.4,212.5c-8.1,6-17,10.2-26.9,12.1c-7.2,1.4-13.6-1.3-20-3.6c-6.4-2.3-13.1-5.3-17.7-9.7   c-7.4-7-14.1-14.9-18.5-24.6c-3.4-7.7-4.1-15.6-4.8-23.6c-0.6-8.1-0.7-16.4-0.7-24.5c0-13.9-0.5-28,4.6-41.5   c5.7-15,16.7-22.9,32-26.1c9.7-2,19.3-2.3,29-1.8c9.5,0.5,19,2.1,27.5,6.9c10.8,6.1,16.6,15.7,19.5,27.5c2.3,9.1,2.3,18.3,2.4,27.5   c0.1,11.8,0,23.7-0.5,35.5c-0.6,13.5-5.9,25.2-14.5,35.5c-0.7,0.8-2.9,2.9-5.1,5C334.1,209.4,331.2,211.8,330.4,212.5z"></path>
          <path d="M358.4,138c8.4-0.5,10.7,3.2,12.4,9c2.8,9.4,2.2,18.6-3.5,27c-2.8,4.1-6.6,6.8-12,6"></path>
          <path d="M240.4,138c-6.3-1.2-12.1,2.7-13.8,13c-1.3,7.9-0.6,16,4.7,23c3.1,4.1,6.7,6.7,12,6"></path>
        </g>

        {/* Detallitos esteticos */}
        <g>
          <path d="M226.4,909.5c-0.6,3.3,0.6,6.4,1.1,9.5c1.3,8.3-1.4,16.7,0.9,25c1.9,6.9,2.8,14.2,10.5,17.5"></path>
        </g>
        <g>
          <path d="M375.9,910.5c-1.4,11.1,0.5,22.4-1.6,33.5c-1.3,6.7-2.6,13.7-9.9,17"></path>
        </g>
        <g>
          <path d="M272.9,917.5c1.1,10.7,1.5,21.4-0.5,32c-0.8,4.4-2.2,8.7-5.5,12"></path>
        </g>
        <g>
          <path d="M364.4,264c-9.5,1.3-19,2.5-28.5,4.1c-5.3,0.9-10.6,2.2-14.5,6.4"></path>
        </g>
        <g>
          <path d="M330.9,918.5c-3,0.7-1.8,3.6-1.8,5c-0.2,9.4-0.9,19,1.8,28.3c1.1,3.6,2.3,7,5,9.7"></path>
        </g>
        <g>
          <path d="M301.9,584c4-3.9,5.4-11.4,3-16c-1.6-3-5.1-3.3-7-0.5c-2,3-1,13.4,1.5,16c0.6,0.7,1.3,0.4,2,0.5"></path>
        </g>
        <g>
          <path d="M237.9,261.5c0.1,4.4,3.9,3.1,6,3.4c9.4,1.4,18.8,1.9,28,4.5c3.6,1.1,6.9,2.4,9.5,5.1"></path>
        </g>

        {/* Zona frontal brazo izquierdo */}
        <g
          data-part="7"
          className={`stroke-gray-700 ${
            isSelected('7') ? 'fill-[#a4c9ff]' : 'hover:fill-[#a4c9ff]'
          }`}
          onClick={() => onPartClick('7')}
        >
          <path d="M243.4,243.1c-12.8,7-26.9,10.6-40.5,15.5c-9.9,3.6-19.5,7.8-28.1,14.3c-12.1,9.2-19.2,21.4-24.4,35.2   c-5.2,13.9-6.7,28.5-9,43c-2.1,13.6-4.2,27.3-6,41c-1.3,10-2,20-3.1,30c-1.3,12-1.9,24.1-4.2,36c-1.5,7.6-5.6,14.7-8.2,22.1   c-5.2,14.6-10.3,29.3-13.4,44.5c-1.9,9.3-2.1,19-3.2,28.5c-0.9,7.8-2.4,15.6-2.9,23.5c-0.5,7.9-1.5,15.7-2.1,23.5   c-0.6,7.4-1.5,14.8-3.5,22c-1.1,3.9-3.4,6.8-7,9.1c-18.5,11.7-30.7,29.2-42.5,47c-2.1,3.2-4.1,6.8-4.1,10.9c0,4.6,3.2,7.4,7.5,5.9   c2.5-0.9,4.8-2.2,6.5-4.4c1.9-2.4,4.4-3.7,7.5-4l0.9,3c0,1.8-12.4,36-12.4,37c0.1,2.3-1.2,5.4,2,6.4c2.7,0.8,4.3-1.4,6-3.3   c2.5-2.9,16.2-32.5,18.5-33.5c0,0-5.5,31.9-5.8,35.9c-0.1,1.7-0.3,3.4,0,5c0.8,4.4,3.9,5.5,7.2,2.5c1.3-1.2,16.1-40.1,16.1-39.7   c-0.4,7.9-9.5,42.1-2.6,42.8c8.6,0.8,14.2-37.2,16.9-38.6l-1.5,30.1c0.4,3.7,5.4,4.4,6.5,0.2c0.3-1.4,6.7-33,7.4-36.7   c1.6-8.5,4.1-16.8,8.2-24.6c3.4-6.5,4.7-13,2.2-20.6c-2.8-8.5-1.3-17.2,2.3-25.4c8.9-20.6,18.2-40.9,26.1-62c6-16.2,9.5-33,11.3-50   c1.2-11.2,2.7-22,7-32.5c4.5-11.2,7.8-23,11.7-34.4c1.9-5.5,2.7-11.6,6.1-16.4c1.9-2.6,2.4-5,2.5-8.1c0.3-6.3-1.2-12.4-2-18.5   c-2-14.5-5.7-28.6-8.2-43c-1.2-6.7-3.8-13.1-4.2-20"></path>
          <path d="M164.4,478.5c-7.7-3.9-15.9-5.4-24.5-5.5"></path>
        </g>

        {/* Zona frontal brazo derecho */}
        <g
          data-part="5"
          className={`stroke-gray-700 ${
            isSelected('5') ? 'fill-[#a4c9ff]' : 'hover:fill-[#a4c9ff]'
          }`}
          onClick={() => onPartClick('5')}
        >
          <path d="M417.5,342c-0.4,6.9-3.1,13.3-4.2,20c-2.5,14.4-6.2,28.5-8.2,43c-0.8,6.2-2.3,12.2-2,18.5   c0.2,3.1,0.7,5.5,2.5,8.1c3.4,4.8,4.2,10.9,6.1,16.4c3.9,11.5,7.2,23.2,11.7,34.4c4.3,10.6,5.8,21.4,7,32.5   c1.8,17.1,5.3,33.8,11.3,50c7.8,21,17.2,41.4,26.1,62c3.6,8.2,5.1,17,2.3,25.4c-2.5,7.6-1.1,14,2.2,20.6c4,7.8,6.6,16.1,8.2,24.6   c0.7,3.7,7.1,35.3,7.4,36.7c1,4.2,6.1,3.5,6.5-0.2l-1.5-30.1c2.8,1.3,8.3,39.4,16.9,38.6c6.9-0.7-2.2-34.8-2.6-42.8   c0-0.3,14.8,38.5,16.1,39.7c3.2,3.1,6.3,2,7.2-2.5c0.3-1.6,0.2-3.3,0-5c-0.3-4-5.8-35.9-5.8-35.9c2.3,1,16,30.6,18.5,33.5   c1.7,2,3.3,4.2,6,3.3c3.2-1,1.8-4.1,2-6.4c0.1-1-12.4-35.2-12.4-37l0.9-3c3.1,0.3,5.6,1.6,7.5,4c1.7,2.2,4.1,3.6,6.5,4.4   c4.3,1.5,7.5-1.3,7.5-5.9c0-4.2-2-7.7-4.1-10.9c-11.8-17.8-24-35.3-42.5-47c-3.6-2.3-5.8-5.1-7-9.1c-2-7.2-2.9-14.6-3.5-22   c-0.6-7.8-1.6-15.6-2.1-23.5c-0.5-7.9-2-15.7-2.9-23.5c-1.1-9.5-1.3-19.1-3.2-28.5c-3.1-15.2-8.2-29.9-13.4-44.5   c-2.6-7.4-6.7-14.5-8.2-22.1c-2.3-11.8-3-23.9-4.2-36c-1.1-10-1.8-20-3.1-30c-1.8-13.7-3.8-27.4-6-41c-2.3-14.5-3.7-29.1-9-43   c-5.2-13.7-12.2-26-24.4-35.2c-8.6-6.5-18.2-10.7-28.1-14.3c-13.6-4.9-27.7-8.6-40.5-15.5"></path>
          <path d="M438.2,478.5c7.7-3.9,15.9-5.4,24.5-5.5"></path>
        </g>

        {/* Zona trasera brazo izquierdo */}
        <g
          data-part="6"
          className={`stroke-gray-700 ${
            isSelected('6') ? 'fill-[#a4c9ff]' : 'hover:fill-[#a4c9ff]'
          }`}
          onClick={() => onPartClick('6')}
        >
          <path d="M841.3,343.2c0.4,6.9,3.1,13.3,4.2,20c2.5,14.4,6.2,28.5,8.2,43c0.8,6.2,2.3,12.2,2,18.5   c-0.2,3.1-0.7,5.5-2.5,8.1c-3.4,4.8-4.2,10.9-6.1,16.4c-3.9,11.5-7.2,23.2-11.7,34.4c-4.3,10.6-5.8,21.4-7,32.5   c-1.8,17.1-5.3,33.8-11.3,50c-7.8,21-17.2,41.4-26.1,62c-3.6,8.2-5.1,17-2.3,25.4c2.5,7.6,1.1,14-2.2,20.6   c-4,7.8-6.6,16.1-8.2,24.6c-0.7,3.7-7.1,35.3-7.4,36.7c-1,4.2-6.1,3.5-6.5-0.2l1.5-30.1c-2.8,1.3-8.3,39.4-16.9,38.6   c-6.9-0.7,2.2-34.8,2.6-42.8c0-0.3-14.8,38.5-16.1,39.7c-3.2,3.1-6.3,2-7.2-2.5c-0.3-1.6-0.2-3.3,0-5c0.3-4,5.8-35.9,5.8-35.9   c-2.3,1-16,30.6-18.5,33.5c-1.7,2-3.3,4.2-6,3.3c-3.2-1-1.8-4.1-2-6.4c-0.1-1,12.4-35.2,12.4-37l-0.9-3c-3.1,0.3-5.6,1.6-7.5,4   c-1.7,2.2-4.1,3.6-6.5,4.4c-4.3,1.5-7.5-1.3-7.5-5.9c0-4.2,2-7.7,4.1-10.9c11.8-17.8,24-35.3,42.5-47c3.6-2.3,5.8-5.1,7-9.1   c2-7.2,2.9-14.6,3.5-22c0.6-7.8,1.6-15.6,2.1-23.5c0.5-7.9,2-15.7,2.9-23.5c1.1-9.5,1.3-19.1,3.2-28.5c3.1-15.2,8.2-29.9,13.4-44.5   c2.6-7.4,6.7-14.5,8.2-22.1c2.3-11.8,3-23.9,4.2-36c1.1-10,1.8-20,3.1-30c1.8-13.7,3.8-27.4,6-41c2.3-14.5,3.7-29.1,9-43   c5.2-13.7,12.2-26,24.4-35.2c8.6-6.5,18.2-10.7,28.1-14.3c13.6-4.9,27.7-8.6,40.5-15.5"></path>
        </g>

        {/* Zona trasera brazo derecho */}
        <g
          data-part="8"
          className={`stroke-gray-700 ${
            isSelected('8') ? 'fill-[#a4c9ff]' : 'hover:fill-[#a4c9ff]'
          }`}
          onClick={() => onPartClick('8')}
        >
          <path d="M1078.1,344.8c-0.4,6.9-3.1,13.3-4.2,20c-2.5,14.4-6.2,28.5-8.2,43c-0.8,6.2-2.3,12.2-2,18.5   c0.2,3.1,0.7,5.5,2.5,8.1c3.4,4.8,4.2,10.9,6.1,16.4c3.9,11.5,7.2,23.2,11.7,34.4c4.3,10.6,5.8,21.4,7,32.5   c1.8,17.1,5.3,33.8,11.3,50c7.8,21,17.2,41.4,26.1,62c3.6,8.2,5.1,17,2.3,25.4c-2.5,7.6-1.1,14,2.2,20.6c4,7.8,6.6,16.1,8.2,24.6   c0.7,3.7,7.1,35.3,7.4,36.7c1,4.2,6.1,3.5,6.5-0.2l-1.5-30.1c2.8,1.3,8.3,39.4,16.9,38.6c6.9-0.7-2.2-34.8-2.6-42.8   c0-0.3,14.8,38.5,16.1,39.7c3.2,3.1,6.3,2,7.2-2.5c0.3-1.6,0.2-3.3,0-5c-0.3-4-5.8-35.9-5.8-35.9c2.3,1,16,30.6,18.5,33.5   c1.7,2,3.3,4.2,6,3.3c3.2-1,1.8-4.1,2-6.4c0.1-1-12.4-35.2-12.4-37l0.9-3c3.1,0.3,5.6,1.6,7.5,4c1.7,2.2,4.1,3.6,6.5,4.4   c4.3,1.5,7.5-1.3,7.5-5.9c0-4.2-2-7.7-4.1-10.9c-11.8-17.8-24-35.3-42.5-47c-3.6-2.3-5.8-5.1-7-9.1c-2-7.2-2.9-14.6-3.5-22   c-0.6-7.8-1.6-15.6-2.1-23.5c-0.5-7.9-2-15.7-2.9-23.5c-1.1-9.5-1.3-19.1-3.2-28.5c-3.1-15.2-8.2-29.9-13.4-44.5   c-2.6-7.4-6.7-14.5-8.2-22.1c-2.3-11.8-3-23.9-4.2-36c-1.1-10-1.8-20-3.1-30c-1.8-13.7-3.8-27.4-6-41c-2.3-14.5-3.7-29.1-9-43   c-5.2-13.7-12.2-26-24.4-35.2c-8.6-6.5-18.2-10.7-28.1-14.3c-13.6-4.9-27.7-8.6-40.5-15.5"></path>
        </g>

        {/* Detallitos esteticos */}
        <g>
          <path d="M924.4,315.5c-0.1,4.9,2.2,9.3,3,14c0.5,3,1.7,5.9,0.5,9"></path>
        </g>
        <g>
          <path d="M995.4,315.5c-1.3,7-3.9,13.8-4.5,21"></path>
        </g>
        <g>
          <path d="M1032.9,579c-4.4-0.3-8.4,1.5-12.5,2.5c-1.5,0.3-3,0.4-4.5,0.5"></path>
        </g>
        <g>
          <path d="M929.9,348.5c0,5,0,10,0,15"></path>
        </g>
        <g>
          <path d="M989.9,348.5c-1.2,4.8-0.4,9.7-0.5,14.5"></path>
        </g>
        <g>
          <path d="M910.9,585.5c4.5,3.3,9,6.7,13.5,10"></path>
        </g>
        <g>
          <path d="M901.9,582.5c-4.4-1.5-8.9-2.9-13.5-3.5"></path>
        </g>
        <g>
          <path d="M821.4,469.5c-0.5,4.7-1,9.3-1.5,14"></path>
        </g>
        <g>
          <path d="M1008.9,586.5c-5.5,1.5-9.6,5.1-13.5,9"></path>
        </g>
        <g>
          <path d="M1095,471.3c0.7,5,0.4,10.1,2,15"></path>
        </g>
        <g>
          <path d="M928.4,373.5c-0.2,4.1-0.8,8.2-2.5,12"></path>
        </g>
        <g>
          <path d="M790.9,480c-1,3.8-2.7,7.4-2.5,11.5"></path>
        </g>
        <g>
          <path d="M337.9,431.5c-4.1-1.9-6.6-5.8-10.2-8.2"></path>
        </g>
        <g>
          <path d="M275.4,423.3c-3.3,2.8-6.7,5.5-10,8.2"></path>
        </g>
        <g>
          <path d="M799.4,464.3c-2.4,3-5.1,6-5,10.2"></path>
        </g>
        <g>
          <path d="M813.9,505.5c-3.5,0.5-4.4,3.1-5,6"></path>
        </g>
        <g>
          <path d="M1098.5,492.3c-0.2,3.4,1.7,6.3,2.5,9.5"></path>
        </g>
        <g>
          <path d="M990.9,376c0.7,3.2,1.3,6.3,2,9.5"></path>
        </g>
      </svg>
    </div>
  );
};

export default BodyView;
