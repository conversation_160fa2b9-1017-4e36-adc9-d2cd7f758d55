'use server';

import { patch } from '@/lib/api';
import { revalidatePath } from 'next/cache';
import {
  MedicalPatientDto,
  UpdatePatientPayloadDto
} from '@/features/patients/types/patient';

export async function updatePatient(
  patientId: string,
  data: UpdatePatientPayloadDto
): Promise<{ success: boolean; message?: string; data?: MedicalPatientDto }> {
  try {
    // TODO: Add permission check here (e.g., checkPermission(Permissions.EDIT_PATIENT_INFO))

    // The patch function (via apiRequest) handles non-ok responses and throws errors.
    // We specify the expected return type T as MedicalPatientDto.
    const updatedPatient = await patch<MedicalPatientDto>(
      `/patient/${patientId}`,
      data
    );

    // Revalidate the dashboard path to reflect changes
    revalidatePath('/dashboard');

    // Return success and the updated patient data
    return { success: true, data: updatedPatient };
  } catch (error) {
    console.error('Error in updatePatient server action:', error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : 'An unexpected error occurred.'
    };
  }
}
