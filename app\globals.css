@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 20 66% 99%;
    --foreground: 20 51% 4%;
    --muted: 20 25% 86%;
    --muted-foreground: 20 7% 25%;
    --popover: 20 66% 98%;
    --popover-foreground: 20 51% 3%;
    --card: 20 66% 98%;
    --card-foreground: 20 51% 3%;
    --border: 20 15% 94%;
    --input: 20 15% 94%;
    --primary: 20 48% 72%;
    --primary-foreground: 20 48% 12%;
    --secondary: 20 12% 92%;
    --secondary-foreground: 20 12% 32%;
    --accent: 20 12% 92%;
    --accent-foreground: 20 12% 32%;
    --destructive: 11 80% 22%;
    --destructive-foreground: 11 80% 82%;
    --ring: 20 48% 72%;
    --radius: 0.5rem;
    --chart-1: 20 48% 72%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 20 66% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 20 25% 86%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 253 43% 3%;
    --foreground: 253 31% 98%;
    --muted: 253 7% 13%;
    --muted-foreground: 253 13% 63%;
    --popover: 253 43% 3%;
    --popover-foreground: 253 31% 98%;
    --card: 253 43% 4%;
    --card-foreground: 253 31% 99%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --primary: 253 91% 58%;
    --primary-foreground: 253 91% 98%;
    --secondary: 253 7% 9%;
    --secondary-foreground: 253 7% 69%;
    --accent: 253 13% 14%;
    --accent-foreground: 253 13% 74%;
    --destructive: 339.2 90.36% 51.18%;
    --destructive-foreground: 0 0% 100%;
    --ring: 253 91% 58%;
    --chart-1: 253 91% 58%;
    --chart-2: 253 13% 74%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 253 43% 4%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 253 91% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 253 91% 58%%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply box-border bg-background text-foreground;
  }
}

@layer utilities {
  .min-h-screen {
    min-height: 100vh; /* Fallback */
    min-height: 100dvh;
  }
  .h-screen {
    height: 100vh; /* Fallback */
    height: 100dvh;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
