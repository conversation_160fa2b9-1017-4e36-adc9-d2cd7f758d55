'use server';

import { cookies } from 'next/headers';
import { ApiUser } from '../lib/types';

/**
 * Obtiene el token de autenticación de las cookies
 * Esta función es para uso interno solamente
 */
export async function getAuthToken() {
  return cookies().get('auth_token')?.value;
}

/**
 * Verifica si el usuario está autenticado
 */
export async function isAuthenticated() {
  const token = getAuthToken();
  return !!token;
}

/**
 * Obtiene los datos del usuario actual
 */
export async function getCurrentUser() {
  try {
    const token = getAuthToken();

    if (!token) {
      return { success: false, error: 'No autorizado' };
    }

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );

    if (!response.ok) {
      throw new Error('Error al obtener datos del usuario');
    }

    const userData = await response.json();

    return {
      success: true,
      user: userData
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
}
