import { fetchAlarms } from '@/features/alarmas/actions/getAlarms';
import { AlarmasChatUI } from '../../../features/alarmas/components/alarmas-chat-ui';

export default async function AlarmasPage() {
  const alarmas = await fetchAlarms();
  // Mostrar loading mientras se verifica cualquiera de las autenticaciones
  if (status === 'loading') {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-lg">Cargando...</div>
      </div>
    );
  }

  return <AlarmasChatUI alarmas={alarmas} />;
}
