'use client';

import React, { useState } from 'react';
import { PATHROUTES } from '@/helpers';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ForgotSchema, type ForgotSchemaType } from '../lib/zod/schema';
import { resetPassword } from '../actions/resetPassword';
import { toast } from 'sonner';

const ForgotPassword = () => {
  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ForgotSchemaType>({
    resolver: zodResolver(ForgotSchema),
    defaultValues: {
      email: ''
    }
  });

  const onSubmit = async (data: ForgotSchemaType) => {
    setLoading(true);
    try {
      const result = await resetPassword(data);
      toast.success(result.message);
      reset();
    } catch (error: any) {
      toast.error(error.message || 'Error al recuperar contraseña');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-[500px] space-y-5 border-2 bg-white px-5 py-3 shadow-lg">
      <p className="text-center text-[28px] font-bold text-bluePrimary">
        Recuperar contraseña
      </p>
      <p className="text-center text-sm">
        Ingresa el correo electrónico con el que te registraste y te enviaremos
        las instrucciones para restablecer tu contraseña.
      </p>

      <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-col">
          <label htmlFor="email">Correo Electronico</label>
          <input
            className="border-red border-2 border-slate-300 bg-inputbg p-2 "
            type="text"
            placeholder="Ingrese su correo electronico"
            {...register('email')}
          />
          {errors.email && (
            <p className="text-xs text-red-500">{errors.email.message}</p>
          )}
        </div>

        <button
          className="flex w-full justify-center rounded-xl bg-bluePrimary py-2 font-bold text-white"
          type="submit"
          disabled={loading}
        >
          {loading ? 'Enviando...' : 'Recuperar contraseña'}
        </button>
      </form>
      <p className="text-center">
        ¿Necesitas iniciar sesión?{' '}
        <Link href={PATHROUTES.LOGIN}>
          <span className="text-bluePrimary">Ingresa aca</span>
        </Link>
      </p>
    </div>
  );
};

export default ForgotPassword;
