/* eslint-disable no-console */
'use client';
import React, { useEffect, useState } from 'react';
import FetchConsultations from '@/features/consultation/actions/fetchConsultation';
import { Mail } from 'lucide-react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Avatar from '@/features/patients/assets/AvarDefault.png';
import NavConsultation from '@/features/consultation/components/NavConsultation';
import PhysicalExamination from '@/features/consultation/components/PhysicalExamination';
import ConditionNotes from '@/features/consultation/components/ConditionNotes';
import ExamPhysical from '@/features/consultation/components/ExamPhysical';
import DiagnosisForm from '@/features/consultation/components/DiagnosisForm';

const ConsultationPage = () => {
  const { id } = useParams();
  const [consultation, setConsultation] = useState<any>(null);

  useEffect(() => {
    const fetchConsultation = async () => {
      try {
        // const response = await fetch(`/api/consultations/${id}`);
        // const data = await response.json();
        // setConsultation(data);

        const consultations = FetchConsultations();
        const foundConsultation = consultations.find((c: any) => c.id === id);
        setConsultation(foundConsultation);
      } catch (error) {
        console.error('Error al cargar la consulta:', error);
      }
    };

    if (id) {
      fetchConsultation();
    }
  }, [id]);

  if (!consultation) {
    return <div>Cargando consulta...</div>;
  }

  return (
    <div className="p-5">
      <div>
        <div className="flex w-full flex-row items-center p-2">
          <Image
            className="h-20 w-20 rounded-[5px]"
            src={Avatar}
            alt="Consultation Avatar"
          />
          <div className="m-2 flex flex-col">
            <h1 className="text-xl font-bold text-bluePrimary">
              {' '}
              {consultation.name}
            </h1>
            <div className="flex flex-row">
              <p className="m-1 text-sm">{consultation.birthdate} -</p>
              <p className="m-1 text-sm">{consultation.age} años -</p>
              <p className="m-1 flex flex-row items-center text-sm">
                <Mail className="text-bluePrimary" /> {consultation.email}
              </p>
            </div>
          </div>
        </div>
        <NavConsultation />
      </div>

      <div className="flex overflow-auto bg-gray-50 p-5">
        <div className="flex flex-col gap-4">
          <section id="condition-notes">
            <ConditionNotes />
          </section>
          <section id="physical-examination">
            <PhysicalExamination />
          </section>
          <section id="exam-physical">
            <ExamPhysical />
          </section>
          <section id="diagnosis-form">
            <DiagnosisForm />
          </section>
        </div>
      </div>
    </div>
  );
};

export default ConsultationPage;
