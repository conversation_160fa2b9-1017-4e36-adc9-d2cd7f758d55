import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Onboarding',
  description: 'Onboarding Steps'
};

export default function OnboardingLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative flex min-h-[90vh] items-center justify-center overflow-y-auto bg-gray-50">
      <div className="absolute right-0 top-0 h-60 w-60 rounded-bl-full border-b-[90px] border-l-[90px] border-[#487FFA] border-opacity-40 bg-transparent"></div>
      <div className="absolute bottom-0 left-0 h-60 w-60 rounded-tr-full border-r-[110px] border-t-[110px] border-[#70c247] border-opacity-50 bg-transparent"></div>

      <div className="w-full max-w-full p-6">{children}</div>
    </div>
  );
}
