'use client';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import useOnboardingStore from '../../../features/onboarding/context/onboardingStore';
import step1 from '../../../features/onboarding/components/step1';
import step2 from '../../../features/onboarding/components/step2';
import step3a from '../../../features/onboarding/components/step3a';
import step3b from '../../../features/onboarding/components/step3b';
import step4 from '../../../features/onboarding/components/step4';
import step5 from '../../../features/onboarding/components/step5';
import step6 from '../../../features/onboarding/components/step6';
import ProgressBar from '../../../features/onboarding/components/progressBar';
import Image from 'next/image';
import { toast } from 'sonner';

interface Props {
  params: { step: string };
}

const stepsMap: {
  [key: string]: React.FC<{ onValidation: (isValid: boolean) => void }>;
} = {
  onboarding: step1,
  '1': step1,
  '2': step2,
  Particulares: step3a,
  Organizacion: step3b,
  '4': step4,
  '5': step5,
  '6': step6
};

const stepOrder = ['1', '2', '3', '4', '5', '6'];

const Page = ({ params }: Props) => {
  const router = useRouter();
  const { step } = params;
  const StepComponent = stepsMap[step];
  const { stepData, selectedType, setStepData, sendDataToBackend } =
    useOnboardingStore();
  const [submitting, setSubmitting] = useState(false);
  const isStepValid = stepData[`valid_${step}`] || false;
  const currentIndex = stepOrder.indexOf(
    step === 'Particulares' || step === 'Organizacion' ? '3' : step
  );
  const isStep3Valid =
    stepData['valid_Particulares'] || stepData['valid_Organizacion'];

  useEffect(() => {
    let lastValidStepIndex = -1;

    for (let i = 0; i < stepOrder.length; i++) {
      if (stepOrder[i] === '3') {
        if (isStep3Valid) {
          lastValidStepIndex = i;
        }
      } else if (stepData[`valid_${stepOrder[i]}`]) {
        lastValidStepIndex = i;
      } else {
        break;
      }
    }

    if (
      currentIndex > lastValidStepIndex + 1 &&
      !(
        currentIndex === 3 &&
        (stepData['valid_Particulares'] || stepData['valid_Organizacion'])
      )
    ) {
      router.push(`/onboarding/${stepOrder[lastValidStepIndex + 1]}`);
    }
  }, [currentIndex, isStep3Valid, router, stepData]);

  const handleValidation = (isValid: boolean) => {
    if (stepData[`valid_${step}`] !== isValid) {
      setStepData(`valid_${step}`, isValid);
    }
  };

  const getStepNumber = () => {
    if (step === 'Particulares' || step === 'Organizacion') {
      return 3;
    }
    return parseInt(step);
  };

  const handleNext = async () => {
    if (currentIndex === stepOrder.length - 1) {
      // Last step - submit data
      try {
        if (submitting) return; // Evitar múltiples envíos

        setSubmitting(true);
        await sendDataToBackend();
        toast.success('¡Onboarding completado con éxito!');
        router.push('/dashboard');
      } catch (error) {
        console.error('Error en onboarding:', error);
        if (error instanceof Error) {
          toast.error(`Error: ${error.message}`);
        } else {
          toast.error(
            'Error al completar el onboarding. Por favor, inténtalo de nuevo.'
          );
        }
      } finally {
        setSubmitting(false);
      }
      return;
    }

    if (!isStepValid) return;

    if (step === '2' && selectedType) {
      router.push(`/onboarding/${selectedType}`);
      return;
    }

    if (step === 'Particulares' || step === 'Organizacion') {
      if (!isStep3Valid) {
        toast.warning('⚠ Debe completar al menos una opción en el paso 3.');
        return;
      }
      router.push('/onboarding/4');
      return;
    }

    // Si estamos en el paso 5, redirigir al dashboard
    if (step === '5') {
      try {
        if (submitting) return;
        setSubmitting(true);
        await sendDataToBackend();
        toast.success('¡Onboarding completado con éxito!');
        router.push('/dashboard');
      } catch (error) {
        console.error('Error en onboarding:', error);
        if (error instanceof Error) {
          toast.error(`Error: ${error.message}`);
        } else {
          toast.error(
            'Error al completar el onboarding. Por favor, inténtalo de nuevo.'
          );
        }
      } finally {
        setSubmitting(false);
      }
      return;
    }

    router.push(`/onboarding/${stepOrder[currentIndex + 1]}`);
  };

  const handlePrev = () => {
    if (step === '4') {
      router.push(`/onboarding/${selectedType}`);
      return;
    }

    if (step === 'Particulares' || step === 'Organizacion') {
      router.push('/onboarding/2');
      return;
    }

    if (currentIndex > 0) {
      router.push(`/onboarding/${stepOrder[currentIndex - 1]}`);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center gap-y-4">
      <div className="relative h-16 w-40">
        <Image
          src={'/truchilogo.png'}
          alt="Logo"
          sizes="fill"
          fill
          className="absolute top-4 object-contain"
        />
      </div>

      <div className="relative flex w-full max-w-xl flex-col items-center justify-center gap-y-4 rounded-lg bg-white p-4 shadow-md sm:p-6">
        <p className="absolute left-1/2 top-0 w-24 -translate-x-1/2 -translate-y-1/2 transform rounded-xl bg-[#BCD0FF] text-center text-sm text-[#487FFA]">
          Paso {getStepNumber()} de 6
        </p>

        <StepComponent onValidation={handleValidation} />

        <div className="flex w-full justify-between">
          <button
            onClick={handlePrev}
            disabled={currentIndex === 0}
            className="w-24 rounded-md border-2 border-solid border-[#487FFA] p-2 text-[#487FFA] hover:bg-[#E8F0FF] disabled:opacity-50"
          >
            Atrás
          </button>
          <button
            onClick={handleNext}
            disabled={!isStepValid || submitting}
            className="w-24 rounded-md bg-[#487FFA] p-2 text-white hover:bg-[#365FB8] disabled:opacity-50"
          >
            {submitting
              ? 'Enviando...'
              : currentIndex === stepOrder.length - 1
              ? 'Finalizar'
              : 'Siguiente'}
          </button>
        </div>
      </div>
      <ProgressBar currentStep={getStepNumber()} totalSteps={6} />
    </div>
  );
};

export default Page;
