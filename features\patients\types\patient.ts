export interface MedicalPatientDto {
  user: {
    id?: number; // Assuming ID might be present
    name: string;
    last_name: string;
    email: string;
    identification_type: string;
    identification_number: string;
    birth_date?: string; // For editing purposes
    age: number; // Calculated by backend
    nationality: string;
    gender?: string; // For editing purposes
    main_diagnostic_cie: string; // Calculated by backend
    phone_prefix: string;
    phone: string;
    image?: string | null; // Optional image URL
  };
  patient: {
    id?: number; // Assuming ID might be present
    direction: string;
    country: string;
    province: string;
    city: string;
    postal_code: string;
    direction_number: string;
    apartment?: string | null; // Optional apartment
    health_care_number?: string | null; // Optional health care number
    // Add other patient-specific fields if necessary
  };
  // Include other top-level fields if the DTO has more than user and patient
}

// Type for the PATCH payload, allowing partial updates
export type UpdatePatientPayloadDto = Partial<{
  user: Partial<MedicalPatientDto['user']>;
  patient: Partial<MedicalPatientDto['patient']>;
}>;
