'use client';

import { useState, useEffect } from 'react';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { AppointmentForm } from '@/features/appoinments/components/AppointmentForm';
import { toast } from 'sonner';
import GetPatients from '@/features/patients/actions/getPatient';
import Agenda from '@/features/appoinments/components/agenda/Agenda';
import { createAppointment } from '@/features/appoinments/actions/createAppointment';
import { getAppointments } from '@/features/appoinments/actions/getAppointments';

// Mock data for the current doctor
// Keeping this for future implementation of doctor-specific appointments
const currentDoctor = {
  id: '1',
  name: 'Dr. <PERSON>',
  specialty: 'Medicina General'
};

const AppointmentsPage = () => {
  const [isAppointmentFormOpen, setIsAppointmentFormOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [patients, setPatients] = useState<any[]>([]);

  // Fetch patients data
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch patients
        const patientsData = await GetPatients();
        // If result is an array (mock data), use it directly
        // If it's an object with data property (API response), use the data property
        setPatients(
          Array.isArray(patientsData) ? patientsData : patientsData.data || []
        );
      } catch (error) {
        console.error('Error fetching patients:', error);
        toast.error('Error al cargar los datos');
      }
    };

    fetchData();
  }, []);

  const handleCreateAppointment = async (data: any) => {
    try {
      // Format the data for the API according to the backend DTO
      const startDate = new Date(data.date);
      const startTime = data.time.split(':');
      startDate.setHours(parseInt(startTime[0]), parseInt(startTime[1]), 0, 0);

      // Calculate end time (adding duration in minutes)
      const endDate = new Date(startDate);
      endDate.setMinutes(endDate.getMinutes() + (data.duration || 30));

      const appointmentData = {
        consultation_reason: data.reason || 'Consulta médica',
        start: startDate.toISOString(),
        end: endDate.toISOString(),
        patient_id: data.patientId,
        physician_id: currentDoctor.id,
        status: 'pendiente' as const,
        comments: data.notes,
        tenant_id: '1' // This should come from the user's context in a real app
      };

      // Call the API
      await createAppointment(appointmentData);

      toast.success('Cita agendada exitosamente');
      setIsAppointmentFormOpen(false);
    } catch (error) {
      console.error('Error creating appointment:', error);
      toast.error('Error al agendar la cita');
    }
  };

  // Function to handle date selection from the calendar
  const handleDateSelect = (date: Date) => {
    // Format the time from the date
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}`;

    setSelectedDate(date);
    setSelectedTime(formattedTime);
    setIsAppointmentFormOpen(true);
  };

  return (
    <div className="container py-6" style={{ height: 'calc(100vh - 80px)' }}>
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Gestión de Turnos</h1>
        <Button
          onClick={() => {
            setSelectedDate(null);
            setSelectedTime('');
            setIsAppointmentFormOpen(true);
          }}
          className="flex items-center gap-2"
        >
          <Plus size={16} /> Nuevo Turno
        </Button>
      </div>

      <div className="h-[calc(100%-60px)]">
        <Agenda onDateSelect={handleDateSelect} />
      </div>

      <AppointmentForm
        isOpen={isAppointmentFormOpen}
        onClose={() => setIsAppointmentFormOpen(false)}
        onSubmit={handleCreateAppointment}
        patients={patients.map((patient) => ({
          id: patient.id,
          name: patient.name
        }))}
        initialDate={selectedDate}
        initialTime={selectedTime}
      />
    </div>
  );
};

export default AppointmentsPage;
