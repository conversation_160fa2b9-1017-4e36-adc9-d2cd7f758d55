'use client';

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

type UploadFile = {
  id: number;
  file: File;
};

type Medication = {
  nombre: string;
  dosis: string;
  frecuencia: string;
  duracion: string;
};

type ConsultationStore = {
  uploadFiles: UploadFile[];
  notes: string;
  addFiles: (files: UploadFile[]) => void;
  removeFile: (id: number) => void;
  setNotes: (text: string) => void;
  comments: { [key: string]: string };
  subsistemas: string[];
  addSubsistema: (subsistema: string) => void;
  removeSubsistema: (subsistema: string) => void;
  updateComment: (subsistema: string, comment: string) => void;
  physicalExams: { [key: string]: string }; // Clave: parte del cuerpo, valor: nota/descripción
  addPhysicalExam: (part: string, note?: string) => void;
  updatePhysicalExamNote: (part: string, note: string) => void;
  removePhysicalExam: (part: string) => void;
  diagnoses: { [key: string]: string }; // Clave: diagnóstico
  addDiagnosis: (diagnosis: string) => void;
  removeDiagnosis: (diagnosis: string) => void;
  updateDiagnosisComment: (diagnosis: string, comment: string) => void;
  requests: { [key: string]: string };
  addRequests: (request: string) => void;
  removeRequest: (request: string) => void;
  treatementPlan: { [key: string]: string };
  addTreatementPlan: (treatement: string) => void;
  removeTreatementPlan: (treatement: string) => void;
  procedures: string[];
  addProcedure: (procedure: string) => void;
  removeProcedure: (procedure: string) => void;
  medications: Medication[]; // Nuevo estado para medicamentos
  instructions: string; // Nuevo estado para instrucciones médicas
  addMedication: (medication: Medication) => void;
  updateMedication: (
    index: number,
    field: keyof Medication,
    value: string
  ) => void;
  removeMedication: (index: number) => void;
  setInstructions: (instructions: string) => void;
};

const useConsultationStore = create<ConsultationStore>()(
  persist(
    (set) => ({
      uploadFiles: [],
      notes: '',
      subsistemas: [],
      comments: {},
      physicalExams: {},
      diagnoses: {},
      requests: {},
      treatementPlan: {},
      procedures: [],
      medications: [],
      instructions: '',

      addFiles: (files) =>
        set((state) => ({
          uploadFiles: [...state.uploadFiles, ...files]
        })),

      removeFile: (id) =>
        set((state) => ({
          uploadFiles: state.uploadFiles.filter((file) => file.id !== id)
        })),

      setNotes: (text) => set({ notes: text }),

      addSubsistema: (subsistema) =>
        set((state) => {
          if (!state.subsistemas.includes(subsistema)) {
            return {
              subsistemas: [...state.subsistemas, subsistema],
              comments: { ...state.comments, [subsistema]: '' }
            };
          }
          return state;
        }),

      removeSubsistema: (subsistema) =>
        set((state) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { [subsistema]: _, ...newComments } = state.comments;
          return {
            subsistemas: state.subsistemas.filter((s) => s !== subsistema),
            comments: newComments
          };
        }),

      updateComment: (subsistema, comment) =>
        set((state) => ({
          comments: { ...state.comments, [subsistema]: comment }
        })),

      addPhysicalExam: (part) =>
        set((state) => ({
          physicalExams: { ...state.physicalExams, [part]: '' }
        })),

      updatePhysicalExamNote: (part, note) =>
        set((state) => ({
          physicalExams: { ...state.physicalExams, [part]: note }
        })),

      removePhysicalExam: (part) =>
        set((state) => {
          const updatedExams = { ...state.physicalExams };
          delete updatedExams[part];
          return { physicalExams: updatedExams };
        }),

      addDiagnosis: (diagnosis) =>
        set((state) => {
          if (!(diagnosis in state.diagnoses)) {
            return {
              diagnoses: { ...state.diagnoses, [diagnosis]: '' }
            };
          }
          return state;
        }),

      removeDiagnosis: (diagnosis) =>
        set((state) => {
          const updatedDiagnoses = { ...state.diagnoses };
          delete updatedDiagnoses[diagnosis];
          return { diagnoses: updatedDiagnoses };
        }),

      updateDiagnosisComment: (diagnosis, comment) =>
        set((state) => ({
          diagnoses: { ...state.diagnoses, [diagnosis]: comment }
        })),

      addRequests: (request) =>
        set((state) => {
          if (!(request in state.requests)) {
            return {
              requests: { ...state.requests, [request]: '' }
            };
          }
          return state;
        }),

      removeRequest: (request) =>
        set((state) => {
          const updatedRequest = { ...state.requests };
          delete updatedRequest[request];
          return { requests: updatedRequest };
        }),

      addTreatementPlan: (treatement) =>
        set((state) => {
          if (!(treatement in state.treatementPlan)) {
            return {
              treatementPlan: { ...state.treatementPlan, [treatement]: '' }
            };
          }
          return state;
        }),

      removeTreatementPlan: (treatement) =>
        set((state) => {
          const updatedTreatement = { ...state.treatementPlan };
          delete updatedTreatement[treatement];
          return { treatementPlan: updatedTreatement };
        }),

      addProcedure: (procedure) =>
        set((state) => ({
          procedures: [...state.procedures, procedure]
        })),

      removeProcedure: (procedure) =>
        set((state) => ({
          procedures: state.procedures.filter((p) => p !== procedure)
        })),

      addMedication: (medication) =>
        set((state) => ({
          medications: [...state.medications, medication]
        })),

      updateMedication: (index, field, value) =>
        set((state) => ({
          medications: state.medications.map((med, i) =>
            i === index ? { ...med, [field]: value } : med
          )
        })),

      removeMedication: (index) =>
        set((state) => ({
          medications: state.medications.filter((_, i) => i !== index)
        })),

      setInstructions: (instructions) => set({ instructions })
    }),
    {
      name: 'consultation-storage',
      storage: createJSONStorage(() => localStorage)
    }
  )
);

export default useConsultationStore;
