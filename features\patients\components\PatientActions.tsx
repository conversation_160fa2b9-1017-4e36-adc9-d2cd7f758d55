'use client';

import {
  Edit,
  Trash2,
  Calendar,
  User,
  FileText,
  MessageCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { PatientActionsProps } from '../types/dashboardTypes';

export function PatientActions({
  patient,
  onViewDetails,
  onViewClinicalHistory,
  onEditPatient,
  onDeletePatient
}: PatientActionsProps) {
  const router = useRouter();

  const handleViewProfile = () => {
    router.push(`/dashboard/pacientes/${patient.id}/perfil`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="text-blue-600">...</button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        side="bottom"
        align="start"
        className="w-48 bg-[#FBFBFB]"
      >
        <DropdownMenuItem
          onSelect={() => {
            toast.info('Funcionalidad en desarrollo');
            console.log('Iniciar consulta');
          }}
          className="hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Iniciar consulta
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={handleViewProfile}
          className="hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600"
        >
          <User className="mr-2 h-4 w-4" />
          Ver Perfil
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => onViewClinicalHistory(patient.id)}
          className="hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600"
        >
          <FileText className="mr-2 h-4 w-4" />
          Ver historial clínico
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => {
            toast.info('Funcionalidad premium en desarrollo');
            console.log('Chatear');
          }}
          className="hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600"
        >
          <MessageCircle className="mr-2 h-4 w-4" />
          Chatear
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onSelect={() => onEditPatient(patient.id)}
          className="text-blue-600 hover:bg-blue-50 focus:bg-blue-50"
        >
          <Edit className="mr-2 h-4 w-4" />
          Editar Datos
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => onDeletePatient(patient)}
          className="text-red-600 hover:bg-red-50 focus:bg-red-50"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Eliminar Paciente
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
