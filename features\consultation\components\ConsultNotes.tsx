'use client';
import React from 'react';
import useConsultationStore from '../context/consultationStore';

const ConsultNotes = () => {
  const { notes, setNotes } = useConsultationStore();

  const saveToBackend = async () => {
    try {
      const response = await fetch('/api/consult-notes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notes })
      });

      if (!response.ok) {
        throw new Error('Error al guardar las notas');
      }
      alert('Notas guardadas exitosamente');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error al enviar las notas:', error);
      alert('Hubo un error al guardar las notas');
    }
  };

  return (
    <div
      className="max-x-md mx-auto w-full rounded-sm border border-[#DCDBDB] bg-[#FDF3D3] p-4"
      style={{
        backgroundImage:
          'repeating-linear-gradient(#FDF3D3, #FDF3D3 40px, #cfd8dc 41px)',
        backgroundSize: '100% 42px'
      }}
    >
      <h1 className="mb-2 text-sm font-semibold text-gray-800">
        Notas de consulta:
      </h1>
      <textarea
        className="min-h-[200px] w-full resize-none bg-transparent text-gray-800 placeholder-gray-500 outline-none focus:ring-2 focus:ring-blue-400"
        placeholder="Escribe tus notas aquí..."
        value={notes}
        onChange={(e) => setNotes(e.target.value)}
        onBlur={saveToBackend}
      ></textarea>
    </div>
  );
};

export default ConsultNotes;
