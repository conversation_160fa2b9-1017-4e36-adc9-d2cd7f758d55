const CheckedIcon = () => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <clipPath id="clip4580_2314">
          <rect width="14" height="14" fill="white" fillOpacity="0" />
        </clipPath>
      </defs>
      <g clipPath="url(#clip4580_2314)">
        <path
          d="M0 0L14 0L14 14L0 14L0 0Z"
          fill="#000000"
          fillOpacity="0"
          fillRule="nonzero"
        />
        <path
          d="M14 0L14 14L0 14L0 0L14 0Z"
          stroke="#000000"
          strokeOpacity="0"
          strokeWidth="2"
          strokeLinejoin="round"
        />
        <path
          d="M1.75 7C1.75 7.69 1.88 8.36 2.14 9C2.41 9.65 2.79 10.22 3.28 10.71C3.77 11.2 4.34 11.58 4.99 11.85C5.63 12.11 6.3 12.25 7 12.25C7.69 12.25 8.36 12.11 9 11.85C9.65 11.58 10.22 11.2 10.71 10.71C11.2 10.22 11.58 9.65 11.85 9C12.11 8.36 12.25 7.69 12.25 7C12.25 6.3 12.11 5.63 11.85 4.99C11.58 4.34 11.2 3.77 10.71 3.28C10.22 2.79 9.65 2.41 9 2.14C8.36 1.88 7.69 1.74 7 1.74C6.3 1.74 5.63 1.88 4.99 2.14C4.34 2.41 3.77 2.79 3.28 3.28C2.79 3.77 2.41 4.34 2.14 4.99C1.88 5.63 1.75 6.3 1.75 7C1.75 7.69 1.88 8.36 2.14 9Z"
          stroke="#70C247"
          strokeOpacity="1"
          strokeWidth="1"
          strokeLinejoin="round"
        />
        <path
          d="M5.25 7L6.41 8.16L8.75 5.83"
          stroke="#70C247"
          strokeOpacity="1"
          strokeWidth="1"
          strokeLinejoin="round"
          strokeLinecap="round"
        />
      </g>
    </svg>
  );
};

export default CheckedIcon;
