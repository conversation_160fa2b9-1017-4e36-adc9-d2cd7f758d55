'use client';
import { useEffect, useState, useCallback } from 'react';
import useOnboardingStore from '../context/onboardingStore';
import { toast } from 'sonner';
import { sendOTP, verifyOTP } from '../../auth/actions/otpActions';

const Step1 = ({
  onValidation
}: {
  onValidation: (isValid: boolean) => void;
}) => {
  const { stepData, setStepData } = useOnboardingStore();
  const [mounted, setMounted] = useState(false);
  const [showCodeInput, setShowCodeInput] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const userName = stepData.name || '';
  const userId = stepData.userId || '';

  useEffect(() => {
    setMounted(true);

    // Inicializar el estado de validación basado en otpVerified
    if (stepData.otpVerified) {
      setIsValid(true);
    }
  }, []);

  // Usar useCallback para evitar recrear la función en cada render
  const updateValidation = useCallback(
    (valid: boolean) => {
      if (isValid !== valid) {
        setIsValid(valid);
        onValidation(valid);
      }
    },
    [isValid, onValidation]
  );

  const phone = mounted ? stepData.phone || '' : '';
  const code = mounted ? stepData.code || '' : '';

  const errors = {
    phone:
      phone.length === 10 && /^[0-9]+$/.test(phone)
        ? ''
        : 'El número de teléfono debe tener 10 dígitos.',
    code:
      code.length === 6 && /^[0-9]+$/.test(code)
        ? ''
        : 'El código de verificación debe tener 6 dígitos.'
  };

  // Este efecto solo maneja la validación del paso
  useEffect(() => {
    if (!mounted) return;

    // Si el código ya está verificado, validamos el paso
    if (stepData.otpVerified) {
      updateValidation(true);
    } else {
      updateValidation(false);
    }
  }, [mounted, stepData.otpVerified, updateValidation]);

  const handleRequestCode = async () => {
    if (errors.phone) return;

    try {
      // Simular envío de código
      setShowCodeInput(true);
      toast.success('Código enviado correctamente');
    } catch (error) {
      console.error('Error al enviar código:', error);
      if (error instanceof Error) {
        toast.error(`Error: ${error.message}`);
      } else {
        toast.error('Hubo un error al enviar el código');
      }
    }
  };

  const verifyCode = async () => {
    if (errors.code || !code || !userId || verifying) return;

    try {
      setVerifying(true);
      // Simular verificación exitosa
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simular delay

      // Marcar como verificado en el store
      setStepData('otpVerified', true);
      toast.success('Código verificado correctamente');
    } catch (error) {
      console.error('Error al verificar código:', error);
      if (error instanceof Error) {
        toast.error(`Error: ${error.message}`);
      } else {
        toast.error('Código incorrecto o expirado');
      }
    } finally {
      setVerifying(false);
    }
  };

  return (
    <>
      <div className="flex w-full flex-col items-center space-y-6 sm:p-4">
        <h1 className="text-center text-2xl font-bold text-[#487FFA]">
          {userName
            ? `${userName}, verifica tu identidad`
            : 'Verifica tu identidad'}
        </h1>

        <p className="text-start text-base text-[#5F5F5F] sm:text-sm">
          Por favor ingresa el código recibido en tu teléfono celular.
        </p>

        <div className="flex w-full flex-col space-y-4">
          <div className="flex flex-col space-y-2">
            <label htmlFor="phone" className="text-sm text-[#5F5F5F]">
              Celular
            </label>
            <div className="flex flex-row">
              <select
                name="country-code"
                id="country-code"
                className="rounded-md border border-gray-300 bg-white text-sm focus:border-[#487FFA] focus:outline-none focus:ring focus:ring-[#487FFA]"
              >
                <option value="+54">🇦🇷 +54</option>
                <option value="+1">🇺🇸 +1</option>
                <option value="+44">🇬🇧 +44</option>
                <option value="+34">🇪🇸 +34</option>
                <option value="+55">🇧🇷 +55</option>
              </select>
              <input
                id="phone"
                type="text"
                value={phone}
                onChange={(e) => setStepData('phone', e.target.value)}
                className="w-full rounded-md border border-gray-300 bg-white p-2 text-sm focus:border-[#487FFA] focus:outline-none focus:ring focus:ring-[#487FFA]"
              />
            </div>
            {errors.phone && (
              <p className="text-sm text-red-500">{errors.phone}</p>
            )}
          </div>

          {!showCodeInput && (
            <div className="flex w-full justify-center">
              <button
                onClick={handleRequestCode}
                className="cursor-pointer rounded-md bg-[#70c247] px-4 py-2 text-lg text-white hover:bg-[#5ba839]"
              >
                Solicitar código
              </button>
            </div>
          )}

          {showCodeInput && (
            <>
              <div className="flex flex-col space-y-2">
                <label htmlFor="code" className="text-sm text-[#5F5F5F]">
                  Codigo de verificación
                </label>
                <input
                  id="code"
                  type="text"
                  value={code}
                  onChange={(e) => setStepData('code', e.target.value)}
                  className="w-full rounded-md border border-gray-300 bg-white p-2 text-sm focus:border-[#487FFA] focus:outline-none focus:ring focus:ring-[#487FFA]"
                  placeholder="Ingrese el código de verificación"
                />
                {errors.code && (
                  <p className="text-sm text-red-500">{errors.code}</p>
                )}
              </div>

              <div className="flex w-full justify-between">
                <button
                  onClick={verifyCode}
                  disabled={verifying || errors.code !== ''}
                  className="cursor-pointer rounded-md bg-[#487FFA] px-4 py-2 text-sm text-white hover:bg-[#365FB8] disabled:opacity-50"
                >
                  {verifying ? 'Verificando...' : 'Verificar código'}
                </button>
                <button
                  onClick={handleRequestCode}
                  className="cursor-pointer rounded-md px-4 py-2 text-sm text-[#487FFA] hover:bg-[#487FFA] hover:text-white"
                >
                  Reenviar código
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Step1;
