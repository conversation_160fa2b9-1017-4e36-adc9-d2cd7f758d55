'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

// Define the schema for consultation data
const consultationSchema = z.object({
  // Basic information
  patientId: z.string({
    required_error: 'El paciente es requerido'
  }),
  appointmentId: z.string().optional(),

  // Vital signs
  temperature: z.string().optional(),
  bloodPressureSystolic: z.string().optional(),
  bloodPressureDiastolic: z.string().optional(),
  heartRate: z.string().optional(),
  respiratoryRate: z.string().optional(),
  oxygenSaturation: z.string().optional(),
  weight: z.string().optional(),
  height: z.string().optional(),

  // Consultation details
  reason: z.string().min(5, 'El motivo debe tener al menos 5 caracteres'),
  symptoms: z.string().min(5, 'Los síntomas deben tener al menos 5 caracteres'),
  physicalExamination: z.string().optional(),
  diagnosis: z
    .string()
    .min(3, 'El diagnóstico debe tener al menos 3 caracteres'),
  treatment: z
    .string()
    .min(3, 'El tratamiento debe tener al menos 3 caracteres'),
  observations: z.string().optional(),

  // Prescriptions
  medications: z
    .array(
      z.object({
        name: z.string().min(3, 'El nombre debe tener al menos 3 caracteres'),
        dosage: z.string().min(1, 'La dosis es requerida'),
        frequency: z.string().min(1, 'La frecuencia es requerida'),
        duration: z.string().min(1, 'La duración es requerida'),
        instructions: z.string().optional()
      })
    )
    .optional(),

  // Lab tests
  labTests: z.array(z.string()).optional(),

  // Follow-up
  requiresFollowUp: z.boolean().optional(),
  followUpDate: z.string().optional(),
  followUpNotes: z.string().optional()
});

type ConsultationFormData = z.infer<typeof consultationSchema>;

// Mock data for lab tests
const availableLabTests = [
  { id: '1', name: 'Hemograma completo' },
  { id: '2', name: 'Perfil lipídico' },
  { id: '3', name: 'Glucemia' },
  { id: '4', name: 'Función renal' },
  { id: '5', name: 'Función hepática' },
  { id: '6', name: 'Radiografía de tórax' },
  { id: '7', name: 'Electrocardiograma' },
  { id: '8', name: 'Ecografía abdominal' }
];

interface ConsultationFormProps {
  onSubmit: (data: ConsultationFormData) => Promise<void>;
  patients: Array<{ id: string; name: string }>;
  appointments?: Array<{
    id: string;
    date: string;
    time: string;
    patientId: string;
  }>;
  initialPatientId?: string;
  initialAppointmentId?: string;
  onCancel?: () => void;
}

export function ConsultationForm({
  onSubmit,
  patients,
  appointments = [],
  initialPatientId,
  initialAppointmentId,
  onCancel
}: ConsultationFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedLabTests, setSelectedLabTests] = useState<string[]>([]);
  const [medications, setMedications] = useState<
    Array<{
      id: string;
      name: string;
      dosage: string;
      frequency: string;
      duration: string;
      instructions: string;
    }>
  >([]);

  const {
    register,
    handleSubmit,
    control,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<ConsultationFormData>({
    resolver: zodResolver(consultationSchema),
    defaultValues: {
      patientId: initialPatientId || '',
      appointmentId: initialAppointmentId || '',
      reason: '',
      symptoms: '',
      physicalExamination: '',
      diagnosis: '',
      treatment: '',
      observations: '',
      requiresFollowUp: false,
      followUpDate: '',
      followUpNotes: ''
    }
  });

  const selectedPatientId = watch('patientId');
  const requiresFollowUp = watch('requiresFollowUp');

  // Function to handle form submission
  const handleFormSubmit = async (data: ConsultationFormData) => {
    setIsSubmitting(true);
    try {
      // Add medications and lab tests to the form data
      data.medications = medications.map((med) => ({
        name: med.name,
        dosage: med.dosage,
        frequency: med.frequency,
        duration: med.duration,
        instructions: med.instructions
      }));

      data.labTests = selectedLabTests;

      await onSubmit(data);
      toast.success('Consulta registrada exitosamente');
      reset();
      setMedications([]);
      setSelectedLabTests([]);
      if (onCancel) {
        onCancel();
      } else {
        router.push('/dashboard/consultas');
      }
    } catch (error) {
      toast.error('Error al registrar la consulta');
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to add a new medication
  const addMedication = () => {
    setMedications([
      ...medications,
      {
        id: Date.now().toString(),
        name: '',
        dosage: '',
        frequency: '',
        duration: '',
        instructions: ''
      }
    ]);
  };

  // Function to update a medication
  const updateMedication = (id: string, field: string, value: string) => {
    setMedications(
      medications.map((med) =>
        med.id === id ? { ...med, [field]: value } : med
      )
    );
  };

  // Function to remove a medication
  const removeMedication = (id: string) => {
    setMedications(medications.filter((med) => med.id !== id));
  };

  // Function to toggle a lab test
  const toggleLabTest = (testId: string) => {
    if (selectedLabTests.includes(testId)) {
      setSelectedLabTests(selectedLabTests.filter((id) => id !== testId));
    } else {
      setSelectedLabTests([...selectedLabTests, testId]);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6 flex items-center">
        <Button
          variant="ghost"
          className="mr-4"
          onClick={() => {
            if (onCancel) {
              onCancel();
            } else {
              router.push('/dashboard/consultas');
            }
          }}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Volver
        </Button>
        <h1 className="text-3xl font-bold">Registrar Consulta Médica</h1>
      </div>

      <Card>
        <CardContent className="pt-6">
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="basic">Información Básica</TabsTrigger>
                <TabsTrigger value="vitals">Signos Vitales</TabsTrigger>
                <TabsTrigger value="consultation">Consulta</TabsTrigger>
                <TabsTrigger value="prescriptions">Prescripciones</TabsTrigger>
                <TabsTrigger value="followup">Seguimiento</TabsTrigger>
              </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="grid gap-2">
                    <Label htmlFor="patientId">Paciente *</Label>
                    <Select
                      onValueChange={(value) => setValue('patientId', value)}
                      defaultValue={initialPatientId}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccione un paciente" />
                      </SelectTrigger>
                      <SelectContent>
                        {patients.map((patient) => (
                          <SelectItem key={patient.id} value={patient.id}>
                            {patient.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.patientId && (
                      <p className="text-xs text-red-500">
                        {errors.patientId.message}
                      </p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="appointmentId">Cita (opcional)</Label>
                    <Select
                      onValueChange={(value) =>
                        setValue('appointmentId', value)
                      }
                      defaultValue={initialAppointmentId}
                      disabled={!selectedPatientId}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccione una cita" />
                      </SelectTrigger>
                      <SelectContent>
                        {appointments
                          .filter(
                            (appt) => appt.patientId === selectedPatientId
                          )
                          .map((appointment) => (
                            <SelectItem
                              key={appointment.id}
                              value={appointment.id}
                            >
                              {format(
                                new Date(appointment.date),
                                'dd/MM/yyyy',
                                {
                                  locale: es
                                }
                              )}{' '}
                              - {appointment.time}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="reason">Motivo de la Consulta *</Label>
                  <Textarea
                    id="reason"
                    {...register('reason')}
                    placeholder="Describa el motivo de la consulta"
                    className="min-h-[80px]"
                  />
                  {errors.reason && (
                    <p className="text-xs text-red-500">
                      {errors.reason.message}
                    </p>
                  )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="symptoms">Síntomas *</Label>
                  <Textarea
                    id="symptoms"
                    {...register('symptoms')}
                    placeholder="Describa los síntomas del paciente"
                    className="min-h-[80px]"
                  />
                  {errors.symptoms && (
                    <p className="text-xs text-red-500">
                      {errors.symptoms.message}
                    </p>
                  )}
                </div>
              </TabsContent>

              {/* Vital Signs Tab */}
              <TabsContent value="vitals" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="grid gap-2">
                    <Label htmlFor="temperature">Temperatura (°C)</Label>
                    <Input
                      id="temperature"
                      type="number"
                      step="0.1"
                      {...register('temperature')}
                      placeholder="36.5"
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label>Presión Arterial (mmHg)</Label>
                    <div className="flex gap-2">
                      <Input
                        id="bloodPressureSystolic"
                        type="number"
                        {...register('bloodPressureSystolic')}
                        placeholder="120"
                        className="w-1/2"
                      />
                      <span className="flex items-center">/</span>
                      <Input
                        id="bloodPressureDiastolic"
                        type="number"
                        {...register('bloodPressureDiastolic')}
                        placeholder="80"
                        className="w-1/2"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="grid gap-2">
                    <Label htmlFor="heartRate">Frecuencia Cardíaca (lpm)</Label>
                    <Input
                      id="heartRate"
                      type="number"
                      {...register('heartRate')}
                      placeholder="70"
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="respiratoryRate">
                      Frecuencia Respiratoria (rpm)
                    </Label>
                    <Input
                      id="respiratoryRate"
                      type="number"
                      {...register('respiratoryRate')}
                      placeholder="16"
                    />
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="grid gap-2">
                    <Label htmlFor="oxygenSaturation">
                      Saturación de Oxígeno (%)
                    </Label>
                    <Input
                      id="oxygenSaturation"
                      type="number"
                      {...register('oxygenSaturation')}
                      placeholder="98"
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="weight">Peso (kg)</Label>
                    <Input
                      id="weight"
                      type="number"
                      step="0.1"
                      {...register('weight')}
                      placeholder="70.5"
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="height">Altura (cm)</Label>
                  <Input
                    id="height"
                    type="number"
                    {...register('height')}
                    placeholder="175"
                  />
                </div>
              </TabsContent>

              {/* Consultation Tab */}
              <TabsContent value="consultation" className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="physicalExamination">Examen Físico</Label>
                  <Textarea
                    id="physicalExamination"
                    {...register('physicalExamination')}
                    placeholder="Describa los hallazgos del examen físico"
                    className="min-h-[100px]"
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="diagnosis">Diagnóstico *</Label>
                  <Textarea
                    id="diagnosis"
                    {...register('diagnosis')}
                    placeholder="Ingrese el diagnóstico"
                    className="min-h-[80px]"
                  />
                  {errors.diagnosis && (
                    <p className="text-xs text-red-500">
                      {errors.diagnosis.message}
                    </p>
                  )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="treatment">Tratamiento *</Label>
                  <Textarea
                    id="treatment"
                    {...register('treatment')}
                    placeholder="Describa el tratamiento recomendado"
                    className="min-h-[80px]"
                  />
                  {errors.treatment && (
                    <p className="text-xs text-red-500">
                      {errors.treatment.message}
                    </p>
                  )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="observations">Observaciones</Label>
                  <Textarea
                    id="observations"
                    {...register('observations')}
                    placeholder="Observaciones adicionales"
                    className="min-h-[80px]"
                  />
                </div>
              </TabsContent>

              {/* Prescriptions Tab */}
              <TabsContent value="prescriptions" className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Medicamentos</h3>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addMedication}
                    >
                      Agregar Medicamento
                    </Button>
                  </div>

                  {medications.length === 0 ? (
                    <div className="flex h-20 items-center justify-center rounded-md border border-dashed border-gray-300">
                      <p className="text-gray-500">
                        No hay medicamentos agregados
                      </p>
                    </div>
                  ) : (
                    medications.map((medication, index) => (
                      <div
                        key={medication.id}
                        className="rounded-md border border-gray-200 p-4"
                      >
                        <div className="mb-2 flex items-center justify-between">
                          <h4 className="font-medium">
                            Medicamento {index + 1}
                          </h4>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-8 text-red-600 hover:bg-red-50 hover:text-red-700"
                            onClick={() => removeMedication(medication.id)}
                          >
                            Eliminar
                          </Button>
                        </div>
                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="grid gap-2">
                            <Label>Nombre *</Label>
                            <Input
                              value={medication.name}
                              onChange={(e) =>
                                updateMedication(
                                  medication.id,
                                  'name',
                                  e.target.value
                                )
                              }
                              placeholder="Nombre del medicamento"
                            />
                          </div>
                          <div className="grid gap-2">
                            <Label>Dosis *</Label>
                            <Input
                              value={medication.dosage}
                              onChange={(e) =>
                                updateMedication(
                                  medication.id,
                                  'dosage',
                                  e.target.value
                                )
                              }
                              placeholder="Ej: 500mg"
                            />
                          </div>
                        </div>
                        <div className="mt-2 grid gap-4 md:grid-cols-2">
                          <div className="grid gap-2">
                            <Label>Frecuencia *</Label>
                            <Input
                              value={medication.frequency}
                              onChange={(e) =>
                                updateMedication(
                                  medication.id,
                                  'frequency',
                                  e.target.value
                                )
                              }
                              placeholder="Ej: Cada 8 horas"
                            />
                          </div>
                          <div className="grid gap-2">
                            <Label>Duración *</Label>
                            <Input
                              value={medication.duration}
                              onChange={(e) =>
                                updateMedication(
                                  medication.id,
                                  'duration',
                                  e.target.value
                                )
                              }
                              placeholder="Ej: 7 días"
                            />
                          </div>
                        </div>
                        <div className="mt-2 grid gap-2">
                          <Label>Instrucciones</Label>
                          <Textarea
                            value={medication.instructions}
                            onChange={(e) =>
                              updateMedication(
                                medication.id,
                                'instructions',
                                e.target.value
                              )
                            }
                            placeholder="Instrucciones adicionales"
                            className="min-h-[60px]"
                          />
                        </div>
                      </div>
                    ))
                  )}
                </div>

                <div className="mt-6 space-y-4">
                  <h3 className="text-lg font-medium">
                    Estudios de Laboratorio
                  </h3>
                  <div className="grid gap-2 md:grid-cols-2">
                    {availableLabTests.map((test) => (
                      <div
                        key={test.id}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={`lab-test-${test.id}`}
                          checked={selectedLabTests.includes(test.id)}
                          onCheckedChange={() => toggleLabTest(test.id)}
                        />
                        <Label
                          htmlFor={`lab-test-${test.id}`}
                          className="cursor-pointer"
                        >
                          {test.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>

              {/* Follow-up Tab */}
              <TabsContent value="followup" className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="requiresFollowUp"
                    checked={requiresFollowUp}
                    onCheckedChange={(checked) =>
                      setValue('requiresFollowUp', checked as boolean)
                    }
                  />
                  <Label htmlFor="requiresFollowUp" className="cursor-pointer">
                    Requiere seguimiento
                  </Label>
                </div>

                {requiresFollowUp && (
                  <>
                    <div className="grid gap-2">
                      <Label htmlFor="followUpDate">Fecha de Seguimiento</Label>
                      <Input
                        id="followUpDate"
                        type="date"
                        {...register('followUpDate')}
                      />
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="followUpNotes">
                        Notas de Seguimiento
                      </Label>
                      <Textarea
                        id="followUpNotes"
                        {...register('followUpNotes')}
                        placeholder="Instrucciones para el seguimiento"
                        className="min-h-[80px]"
                      />
                    </div>
                  </>
                )}
              </TabsContent>
            </Tabs>

            <div className="flex justify-end space-x-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  if (onCancel) {
                    onCancel();
                  } else {
                    router.push('/dashboard/consultas');
                  }
                }}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Guardando...' : 'Guardar Consulta'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
