{"name": "nextshcdndashboardstarter", "version": "1.0.0", "private": true, "author": {"name": "<PERSON>", "url": "https://github.com/Kiranism"}, "scripts": {"dev": "next dev --turbo -p 3001", "mock:server": "json-server --watch features/auth/lib/json-server/db.json --port 4000", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier . --write", "prepare": "husky"}, "lint-staged": {"**/*": ["prettier --write --ignore-unknown"]}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-table": "^8.10.7", "@types/axios": "^0.9.36", "@types/node": "20.5.7", "@types/react": "^18.3.17", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "axios": "^1.8.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.0.4", "date-fns": "^2.30.0", "eslint": "8.48.0", "eslint-config-next": "^14.0.1", "js-cookie": "^3.0.5", "libphonenumber-js": "^1.11.17", "lucide-react": "^0.447.0", "match-sorter": "^6.3.4", "next": "^14.2.3", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "nextjs-toploader": "^1.6.12", "nuqs": "^1.19.1", "picocolors": "^1.1.1", "postcss": "8.4.28", "react": "^18.2.0", "react-big-calendar": "^1.17.1", "react-country-state-city": "^1.1.12", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.47.0", "react-icons": "^5.5.0", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.10", "react-quill": "^2.0.0", "react-responsive": "^10.0.0", "recharts": "^2.12.7", "shadcn": "^2.1.8", "sharp": "^0.32.5", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "sort-by": "^1.2.0", "tailwind-merge": "^1.14.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "uuid": "^9.0.1", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@faker-js/faker": "^9.0.3", "@types/js-cookie": "^3.0.6", "@types/react-big-calendar": "^1.16.1", "@types/react-phone-number-input": "^3.1.37", "@types/sort-by": "^1.2.3", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.11.0", "eslint-import-resolver-alias": "^1.1.2", "husky": "^9.0.11", "json-server": "^1.0.0-beta.3", "lint-staged": "^15.2.7", "prettier": "3.0.3", "prettier-plugin-tailwindcss": "^0.5.14"}}