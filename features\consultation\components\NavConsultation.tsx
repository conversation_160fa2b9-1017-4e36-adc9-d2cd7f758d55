'use client';
import React from 'react';

const NavConsultation = () => {
  return (
    <div className="w-full border border-solid">
      <nav>
        <ul className="flex flex-row ">
          <li>
            {' '}
            <a
              href="#condition-notes"
              className="m-2 text-[#808080] hover:text-bluePrimary"
            >
              Notas de Padecimiento
            </a>
          </li>
          <li>
            {' '}
            <a
              href="#physical-examination"
              className="m-2 text-[#808080] hover:text-bluePrimary"
            >
              Exploración física
            </a>
          </li>
          <li>
            {' '}
            <a
              href="#exam-physical"
              className="m-2 text-[#808080] hover:text-bluePrimary"
            >
              Examen físico
            </a>
          </li>
          <li>
            {' '}
            <a
              href="#diagnosis-form"
              className="m-2 text-[#808080] hover:text-bluePrimary"
            >
              Diagnóstico y tratamiento
            </a>
          </li>
        </ul>
      </nav>
    </div>
  );
};

export default NavConsultation;
