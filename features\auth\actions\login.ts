'use server';

import { LoginSchema, LoginSchemaType } from '../lib/zod/schema';
import { LoginResponse, isValidationError, AuthError } from '../lib/types';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

export async function login(data: LoginSchemaType): Promise<LoginResponse> {
  try {
    // Validate data with Zod
    const validatedData = LoginSchema.safeParse(data);

    if (!validatedData.success) {
      throw {
        message: validatedData.error.errors[0].message,
        status: 400,
        code: 'VALIDATION_ERROR'
      } as AuthError;
    }

    // Make API request
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(validatedData.data)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw {
        message: errorData.message || 'Error de autenticación',
        status: response.status,
        code: 'AUTH_ERROR'
      } as AuthError;
    }

    const responseData: LoginResponse = await response.json();

    // Guardar el token en una cookie segura
    cookies().set('auth_token', responseData.jwt, {
      secure: process.env.NODE_ENV === 'production', // Solo en HTTPS en prod
      maxAge: 60 * 60 * 24 * 7, // Expira en 7 días
      path: '/', // Disponible en todas las rutas
      sameSite: 'strict' // Protege contra CSRF
    });

    // Si no tiene tenant_id, guardamos la información y luego redirigimos fuera del try/catch
    if (!responseData.user.tenant_id) {
      console.log('No tiene tenant_id');

      // Return a flag indicating redirection is needed
      return {
        user: {
          id: responseData.user.id,
          name: responseData.user.name,
          email: responseData.user.email,
          image: responseData.user.image,
          role: responseData.user.role
        },
        jwt: responseData.jwt,
        shouldRedirect: true
      };
    }

    cookies().set('tenant_id', responseData.user.tenant_id, {
      httpOnly: true, // Protege contra XSS
      secure: process.env.NODE_ENV === 'production', // Solo en HTTPS en prod
      maxAge: 60 * 60 * 24 * 7, // Expira en 7 días
      path: '/', // Disponible en todas las rutas
      sameSite: 'strict' // Protege contra CSRF
    });

    return {
      user: {
        id: responseData.user.id,
        name: responseData.user.name,
        email: responseData.user.email,
        image: responseData.user.image,
        role: responseData.user.role,
        tenant_id: responseData.user.tenant_id
      },
      jwt: responseData.jwt
    };
  } catch (error) {
    // If it's a Zod validation error
    if (isValidationError(error)) {
      throw {
        message: error.errors[0].message,
        status: 400,
        code: 'VALIDATION_ERROR'
      } as AuthError;
    }

    // If it's an API error
    if (
      error &&
      typeof error === 'object' &&
      'message' in error &&
      'status' in error
    ) {
      throw {
        message: error.message || 'Authentication error',
        status: error.status || 401,
        code: 'AUTH_ERROR'
      } as AuthError;
    }

    // Generic error
    throw {
      message: error instanceof Error ? error.message : 'Unknown login error',
      status: 500,
      code: 'UNKNOWN_ERROR'
    } as AuthError;
  }
}

// This function handles the redirect outside of try/catch
export async function loginWithRedirect(data: LoginSchemaType) {
  const result = await login(data);

  // Check if we need to redirect to onboarding
  if (result.user.tenant_id === null || (result as any).shouldRedirect) {
    redirect('/onboarding/1');
  }

  return result;
}
