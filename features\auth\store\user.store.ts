import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface UserState {
  name: string;
  email: string;
  picture: string;
  id: string;
  setUser: (name: string, email: string, picture: string, id: string) => void;
  logout: () => void; // Agregamos el método logout
  status: 'loading' | 'authenticated' | 'unauthenticated';
  setStatus: (status: 'loading' | 'authenticated' | 'unauthenticated') => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      name: '',
      email: '',
      picture: '',
      id: '',
      status: 'unauthenticated',
      setUser: (name, email, picture, id) => set({ name, email, picture, id }),
      setStatus: (status) => set({ status }),
      logout: () =>
        set({
          name: '',
          email: '',
          picture: '',
          id: '',
          status: 'unauthenticated'
        }) // Implementación del logout
    }),
    {
      name: 'user-storage'
    }
  )
);
