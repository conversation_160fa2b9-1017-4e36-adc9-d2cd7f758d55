'use client';
import React from 'react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

const LayoutDinamic = ({ children }: { children: React.ReactNode }) => {
  const pathname = usePathname();
  const isRegisterPage = pathname === '/auth/register';

  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen overflow-hidden">
      {/* Esquinas ? Checar */}
      {isRegisterPage ? (
        <>
          <div className="absolute right-0 top-0 h-60 w-60 rounded-bl-full border-b-[90px] border-l-[90px] border-bluePrimary border-opacity-40 bg-transparent"></div>
          <div className="absolute bottom-0 left-0 h-60 w-60 rounded-tr-full border-r-[110px] border-t-[110px] border-[#70c247] border-opacity-50 bg-transparent"></div>
        </>
      ) : (
        <>
          <div className="absolute left-0 top-0 h-60 w-60 rounded-br-full border-b-[90px] border-r-[90px] border-bluePrimary border-opacity-40 bg-transparent"></div>
          <div className="absolute bottom-0 right-0 h-60 w-60 rounded-tl-full border-l-[110px] border-t-[110px] border-[#70c247] border-opacity-50 bg-transparent"></div>
        </>
      )}

      {/* Contenido */}
      {!isRegisterPage && (
        <Image
          src="/truchilogo.png"
          alt="Segimed"
          className="mb-[35px] mt-8 flex h-12 w-40"
          width={500}
          height={500}
        />
      )}

      {children}
    </div>
  );
};

export default LayoutDinamic;
