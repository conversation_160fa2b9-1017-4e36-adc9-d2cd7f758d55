# Segimed

Segimed is a comprehensive medical practice management system designed to streamline patient management, appointment scheduling, and clinical record keeping.

## Development Environment Setup

### Requirements

- Node.js (v18.x or higher)
- npm (v9.x or higher) or yarn (v1.22.x or higher)
- Git

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/your-username/segimed.git
   cd segimed
   ```

2. Install dependencies:

   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up environment variables:

   - Copy the `.env.example.txt` file to `.env.local`
   - Update the values in `.env.local` with your configuration

4. Start the development server:

   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. Start the mock server (for development):

   ```bash
   npm run mock:server
   # or
   yarn mock:server
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## Project Structure

The project follows a feature-based organization:

```
segimed/
├── app/                  # Next.js app directory (pages and layouts)
│   ├── (auth)/           # Authentication-related pages
│   ├── auth/             # Auth pages
│   ├── dashboard/        # Dashboard pages
│   ├── onboarding/       # Onboarding flow
│   └── layout.tsx        # Root layout
├── components/           # Shared UI components
│   ├── ui/               # UI components (shadcn/ui)
│   └── app-sidebar.tsx   # Application sidebar
├── features/             # Feature modules
│   ├── auth/             # Authentication feature
│   ├── patients/         # Patient management feature
│   ├── appoinments/      # Appointment management feature
│   └── onboarding/       # Onboarding feature
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions and libraries
├── public/               # Static assets
├── styles/               # Global styles
└── types/                # TypeScript type definitions
```

### Key Directories

- **app/**: Contains Next.js pages and layouts using the App Router
- **components/**: Reusable UI components used across the application
- **features/**: Feature-specific code organized by domain
- **lib/**: Utility functions, helpers, and configuration

## Development Guide

### Creating New Features

When adding a new feature:

1. Create a new directory in the `features/` folder with the feature name
2. Organize the feature with the following structure:
   ```
   features/feature-name/
   ├── components/        # React components for this feature
   │   └── __tests__/     # Component tests
   ├── actions/           # Data fetching and mutations
   ├── types.ts           # TypeScript types for this feature
   ├── store/             # State management (if needed)
   └── helpers/           # Feature-specific utilities
   ```

### State Management

- Use **React Context** for simple state sharing between components
- Use **Zustand** for more complex global state management
- Use **React Query** for server state management (to be implemented)

### Naming Conventions

- **Files and Directories**: Use kebab-case for directories and files (`patient-list/`)
- **Components**: Use PascalCase for component files (`PatientList.tsx`)
- **Functions**: Use camelCase for functions and variables (`getPatientData()`)
- **Types and Interfaces**: Use PascalCase with descriptive names (`PatientData`)

### Testing

See [TESTING.md](./TESTING.md) for detailed information about testing practices.

## Available Scripts

- `npm run dev`: Start the development server
- `npm run mock:server`: Start the mock API server
- `npm run build`: Build the application for production
- `npm start`: Start the production server
- `npm run lint`: Run ESLint to check for code issues
- `npm run format`: Format code with Prettier
- `npm test`: Run tests

## Contributing

1. Create a new branch for your feature or bugfix
2. Make your changes
3. Write tests for your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.
