'use client';
import PatientCreate from '../../../components/icons/PatientCreate';
import PatientShow from '../../../components/icons/PatientShow';
import useOnboardingStore from '../context/onboardingStore';

const Step6 = ({
  onValidation
}: {
  onValidation: (isValid: boolean) => void;
}) => {
  const { stepData, setStepData } = useOnboardingStore();
  const userName = stepData.name || '';

  const handleSelection = (type: 'sample' | 'create') => {
    setStepData('selectedOption', type);
    onValidation(true);
  };

  return (
    <>
      <div className="flex w-full flex-col items-center space-y-6">
        <h1 className="text-center text-2xl font-bold text-[#487FFA]">
          {userName
            ? `${userName}, ¿cómo quieres comenzar?`
            : '¿Cómo quieres comenzar?'}
        </h1>

        <div className="grid w-full max-w-2xl grid-cols-1 gap-4 sm:grid-cols-2">
          <label
            htmlFor="sample"
            className={`flex cursor-pointer flex-col items-center space-y-2 rounded-lg border p-2 transition-all duration-200 ${
              stepData.selectedOption === 'sample'
                ? 'border-[#487FFA] bg-[#E8EFFF] ring-2 ring-[#487FFA]'
                : 'border-gray-300 bg-white hover:bg-gray-100'
            }`}
          >
            <input
              type="radio"
              id="sample"
              name="option"
              value="sample"
              className="hidden"
              checked={stepData.selectedOption === 'sample'}
              onChange={() => handleSelection('sample')}
            />
            <PatientShow />
            <h2 className="text-sm font-bold text-[#487FFA]">
              Ver un paciente de muestra
            </h2>
            <p className="text-center text-xs">
              Crea un paciente de ejemplo con su historial clínico y una
              consulta realizada
            </p>
          </label>

          <label
            htmlFor="create"
            className={`flex cursor-pointer flex-col items-center space-y-2 rounded-lg border p-2 transition-all duration-200 ${
              stepData.selectedOption === 'create'
                ? 'border-[#487FFA] bg-[#E8EFFF] ring-2 ring-[#487FFA]'
                : 'border-gray-300 bg-white hover:bg-gray-100'
            }`}
          >
            <input
              type="radio"
              id="create"
              name="option"
              value="create"
              className="hidden"
              checked={stepData.selectedOption === 'create'}
              onChange={() => handleSelection('create')}
            />
            <PatientCreate />
            <h2 className="text-sm font-bold text-[#487FFA]">
              Crea tu primer paciente
            </h2>
            <p className="text-center text-xs">
              Crea tu primer paciente desde cero con sus datos reales
            </p>
          </label>
        </div>
      </div>
    </>
  );
};

export default Step6;
