import { z } from 'zod';

// Interfaces base
export interface ApiUser {
  id: string;
  name: string;
  last_name?: string;
  email?: string;
  role?: string;
  image?: string;
  tenant_id?: string;
}

// Interfaces de respuesta base
interface BaseResponse {
  message?: string;
}

// Interfaces de autenticación
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData extends LoginCredentials {
  name: string;
  confirmPassword: string;
}

export interface ResetPasswordData {
  email: string;
}

// Interfaces de respuesta
export interface LoginResponse extends BaseResponse {
  jwt: string;
  user: ApiUser;
  shouldRedirect?: boolean;
}

export interface RegisterResponse extends BaseResponse {
  id: string;
  name: string;
  last_name: string;
}
export interface ResetPasswordResponse extends BaseResponse {}

export interface AuthResponse {
  user: ApiUser;
  jwt?: string;
}

// Interfaces de error
export interface AuthError {
  message: string;
  code?: string;
  status?: number;
}

export interface FetchError extends Error {
  response: {
    data: { message?: string };
    status: number;
  };
}

// Validación
export type ValidationError = z.ZodError;
export const isValidationError = (error: unknown): error is ValidationError => {
  return error instanceof z.ZodError;
};
