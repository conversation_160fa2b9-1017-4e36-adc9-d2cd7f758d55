'use client';

import { useState, useTransition } from 'react'; // Import useTransition
import { cancelAppointment } from '../actions/cancelAppointment'; // Import the action
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { CancellationReason } from '../types';

interface CancelAppointmentDialogProps {
  appointmentId: string; // Add appointmentId prop
  isOpen: boolean;
  onClose: () => void;
  // Remove onCancel prop
  reasons: CancellationReason[];
}

export function CancelAppointmentDialog({
  isOpen,
  onClose,
  appointmentId, // Destructure appointmentId
  reasons
}: CancelAppointmentDialogProps) {
  const [reason, setReason] = useState('');
  const [comments, setComments] = useState('');
  const [notifyPatient, setNotifyPatient] = useState(true); // Keep for potential future use
  const [isPending, startTransition] = useTransition(); // Use transition for loading state

  const handleCancel = () => {
    if (!reason || !appointmentId) return;

    // Find the reason name from the ID
    const selectedReason = reasons.find((r) => r.id === reason);
    if (!selectedReason) {
      console.error('Selected reason not found');
      // Handle error appropriately, maybe show a toast
      return;
    }

    startTransition(async () => {
      try {
        // Call the server action directly
        await cancelAppointment(appointmentId, selectedReason.name);
        // console.log('Appointment cancelled successfully'); // Optional success log
        onClose(); // Close dialog on success
      } catch (error) {
        console.error('Failed to cancel appointment:', error);
        // Handle error appropriately, maybe show a toast to the user
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Borrar</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="reason">Motivo de la cancelación *</Label>
            <Select value={reason} onValueChange={setReason}>
              <SelectTrigger>
                <SelectValue placeholder="Seleccione un motivo" />
              </SelectTrigger>
              <SelectContent>
                {reasons.map((reason) => (
                  <SelectItem key={reason.id} value={reason.id}>
                    {reason.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="comments">Comentarios</Label>
            <Textarea
              id="comments"
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              rows={3}
            />
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="notify"
              checked={notifyPatient}
              onCheckedChange={(checked) =>
                setNotifyPatient(checked as boolean)
              }
            />
            <Label htmlFor="notify">
              Notificar de la cancelación al paciente
            </Label>
          </div>
        </div>
        <div className="flex justify-end gap-3">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleCancel}
            disabled={!reason || isPending} // Disable button if no reason or pending
          >
            {isPending ? 'Borrando...' : 'Borrar'} {/* Show loading text */}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
