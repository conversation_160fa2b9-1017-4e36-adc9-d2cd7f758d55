'use server';

import {
  Auth<PERSON>rror,
  AuthResponse,
  RegisterResponse,
  isValidationError
} from '../lib/types';
import { RegisterSchema, RegisterSchemaType } from '../lib/zod/register.schema';
import { cookies } from 'next/headers';

export async function registerAction(
  data: RegisterSchemaType
): Promise<AuthResponse> {
  try {
    console.log('Iniciando registro con datos:', data);

    // Validate data with Zod
    const validatedData = RegisterSchema.safeParse(data);

    if (!validatedData.success) {
      console.error('Error de validación:', validatedData.error);
      throw {
        message: validatedData.error.errors[0].message,
        status: 400,
        code: 'VALIDATION_ERROR'
      } as AuthError;
    }

    const { confirmPassword, ...registerData } = validatedData.data;

    console.log('Datos validados (después de limpiar):', registerData);
    console.log('URL de la API:', process.env.NEXT_PUBLIC_API_URL);

    // Make API request using fetch
    try {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'
        }/auth/register`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(registerData)
        }
      );

      console.log(
        'Respuesta del servidor:',
        response.status,
        response.statusText
      );

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error del servidor:', errorData);
        throw {
          message: errorData.message || 'Error de registro',
          status: response.status,
          code: 'AUTH_ERROR'
        } as AuthError;
      }

      const responseData: RegisterResponse = await response.json();
      console.log('Datos de respuesta:', responseData);

      const { id, name, last_name } = responseData;

      cookies().set('user_id', id, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 60 * 60 * 24 * 30, // 30 días
        path: '/'
      });

      return {
        user: {
          id,
          name,
          last_name
        }
      };
    } catch (fetchError) {
      console.error('Error en la petición fetch:', fetchError);
      throw fetchError;
    }
  } catch (error) {
    console.error('Error general en registerAction:', error);

    // If it's a Zod validation error
    if (isValidationError(error)) {
      throw {
        message: error.errors[0].message,
        status: 400,
        code: 'VALIDATION_ERROR'
      } as AuthError;
    }

    // If it's an API error
    if (
      error &&
      typeof error === 'object' &&
      'message' in error &&
      'status' in error
    ) {
      throw {
        message: error.message || 'Registration error',
        status: error.status || 401,
        code: 'AUTH_ERROR'
      } as AuthError;
    }

    // Generic error
    throw {
      message:
        error instanceof Error ? error.message : 'Unknown registration error',
      status: 500,
      code: 'UNKNOWN_ERROR'
    } as AuthError;
  }
}
